const path = require('path');

// all tests which are date dependent will use the GMT time zone
process.env.TZ = 'GMT';

module.exports = {
  projects: [
    {
      displayName: 'contract-tests',
      testMatch: ['<rootDir>/**/*integration*'],
      rootDir: path.resolve(__dirname, 'contract-tests'),
      testPathIgnorePatterns: ['/node_modules/', 'private'],
      transformIgnorePatterns: ['/node_modules/(?!axios).+\\.js$'],
    },
  ],
  globalSetup: path.resolve(__dirname, 'contract-tests/jestGlobalSetup.js'),
  testTimeout: 30000,
  reporters: ['default', 'jest-junit'],
  coverageReporters: [
    'json',
    'lcov',
    'text',
    'clover',
    'text-summary',
    'cobertura',
  ],
};
