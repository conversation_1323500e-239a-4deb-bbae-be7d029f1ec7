import axios, { AxiosHeaders, AxiosRequestHeaders } from 'axios';
import { GraphQLError, print } from 'graphql';
import gql from 'graphql-tag';
import {
  HtbCpsPagedResponse,
  HtbInitSessionResponse,
  HtbInitAnonymousSessionResponse,
  HtbRateResponse,
  HtbChargepoint,
} from './htb.types';
import { env } from './env';

const INIT_ANONYMOUS_SESSION_MUTATION = gql`
  mutation {
    initAnonymousSession {
      sessionToken
      refreshToken
    }
  }
`;

const INIT_SESSION_MUTATION = gql`
  mutation {
    initSession(username: "${env.HTB_COLLECTIVE_GUEST_USER}", password: "${env.HTB_COLLECTIVE_GUEST_PASS}") {
      sessionToken
      refreshToken
    }
  }
`;

const CPS_PAGED_QUERY = gql`
  query cps($pagination: PaginationFilter) {
    cpsPaged(pagination: $pagination) {
      data {
        id
        label
        address
        city
        zip
        country
        publicName
        longitude
        latitude
        additionalFields {
          name
          value
        }
        connectors {
          id
          label
          evseId
          connectorType {
            id
            label
            plugType
            standard
            chargePointType
            maxPowerRating
          }
        }
        information {
          openingTimes {
            description
            regularHours {
              weekday
              operatingHours {
                begin
                end
              }
            }
            exceptionalOpenings {
              begin
              end
            }
            exceptionalClosings {
              begin
              end
            }
          }
          payment
          additional
        }
        contact {
          phone
        }
        status {
          ocpp
          calculated
          simple
        }
        contact {
          phone
          email
        }
        siteId
      }
    }
  }
`;

const CONTRACT_RATE_OPTIONS_QUERY = gql`
  query contractRateOptions {
    contractRateOptions {
      uuid
      name
      description
      shortDescription
    }
  }
`;

type GQLResponse<T> = {
  data: T;
  errors?: Array<GraphQLError>;
};

function authorization(token: string): string {
  return token?.startsWith('Bearer') ? token : `Bearer ${token}`;
}

async function makeRequest<T>(body: unknown, headers: AxiosRequestHeaders) {
  const response = await axios.post<GQLResponse<T>>(env.HTB_URI, body, {
    headers,
  });

  if (response.data.errors?.length) {
    console.error(
      `HasToBe API failed due: ${JSON.stringify(response.data.errors)}`,
      response.data.errors,
    );

    throw response.data.errors[0];
  }

  return response.data.data;
}

function makeGqlRequest<T>(
  query: string,
  variables?: Record<string, unknown>,
  token?: string,
) {
  const body = { query, variables };
  const headers: AxiosRequestHeaders = new AxiosHeaders();

  if (token) {
    headers.authorization = authorization(token);
  }

  return makeRequest<T>(body, headers);
}

async function getTokenAnnon() {
  const query = print(INIT_ANONYMOUS_SESSION_MUTATION);
  const result = await makeGqlRequest<HtbInitAnonymousSessionResponse>(
    query,
    undefined,
    env.HTB_API_KEY,
  );

  return result.initAnonymousSession;
}

async function getToken() {
  const query = print(INIT_SESSION_MUTATION);
  const result = await makeGqlRequest<HtbInitSessionResponse>(
    query,
    undefined,
    env.HTB_API_KEY,
  );

  return result.initSession;
}

const makeAnonymousRequest = (() => {
  let token: string | null;

  return async <T>(query: string, variables?: Record<string, unknown>) => {
    if (!token) {
      const { sessionToken } = await getTokenAnnon();
      token = sessionToken;
    }

    return makeGqlRequest<T>(query, variables, token);
  };
})();

const makeUserRequest = (() => {
  let token: string | null;

  return async <T>(query: string, variables?: Record<string, unknown>) => {
    if (!token) {
      const { sessionToken } = await getToken();
      token = sessionToken;
    }

    return makeGqlRequest<T>(query, variables, token);
  };
})();

export async function queryAllChargepoints(
  from = 0,
  chargepoints: Array<HtbChargepoint> = [],
  batchSize = 100,
): Promise<Array<HtbChargepoint>> {
  const htbChargepoints = await queryChargepoints(from, batchSize);
  const newTotalCps = [...chargepoints, ...htbChargepoints];

  if (htbChargepoints.length < batchSize) {
    return newTotalCps;
  }

  return queryAllChargepoints(newTotalCps.length, newTotalCps, batchSize);
}

export async function queryChargepoints(from: number, size: number) {
  const query = print(CPS_PAGED_QUERY);
  const variables = {
    pagination: {
      limit: size,
      offset: from,
    },
  };

  const result = await makeAnonymousRequest<HtbCpsPagedResponse>(
    query,
    variables,
  );

  return result.cpsPaged.data ?? [];
}

export async function queryRates() {
  const query = print(CONTRACT_RATE_OPTIONS_QUERY);
  const variables = {
    pagination: {
      limit: 500,
      offset: 0,
    },
  };

  const result = await makeUserRequest<HtbRateResponse>(query, variables);
  return result.contractRateOptions;
}
