{"name": "get-data-bpcm", "private": true, "main": "dist/getDataBPCM/src/index.js", "dependencies": {"@elastic/elasticsearch": "7.13.0", "axios": "^1.10.0", "dotenv": "^16.0.2", "graphql": "^16.6.0", "graphql-request": "^5.0.0", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/aws-lambda": "^8.10.93", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.182", "@types/node": "^17.0.23", "babel-jest": "^29.5.0", "jest": "^29.5.0", "lambda-local": "^2.0.2", "ts-node": "^10.9.1", "typescript": "^4.6.3"}}