{"name": "evcpopulatesites", "version": "0.0.1", "description": "Module to create sites objects to post to elastic search", "main": "dist/evcPopulateSites/src/index.js", "scripts": {"prebuild": "rm -rf dist", "build": "npm i && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "dev": "jest --watch", "locally": "node -e \"console.log(require('./dist/evcPopulateSites/src/index').handler(require('./src/__test__/events/s3-sites-event.json')));\"", "start": "ts-node src/index.ts", "test": "jest", "types:check": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.50.0", "axios": "^1.10.0", "csv-string": "^4.1.0", "ngeohash": "^0.6.3", "winston": "^3.7.2"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/aws-lambda": "^8.10.100", "@types/jest": "^27.4.1", "@types/lodash": "^4.14.181", "@types/ngeohash": "^0.6.4", "@types/node": "^18.0.0", "@types/request": "^2.48.8", "@types/unzipper": "^0.10.5", "axios-mock-adapter": "^1.20.0", "babel-jest": "^28.0.2", "jest": "^27.5.1", "local-lambda": "^1.0.0", "ts-node": "^10.7.0", "typescript": "^4.7.3"}}