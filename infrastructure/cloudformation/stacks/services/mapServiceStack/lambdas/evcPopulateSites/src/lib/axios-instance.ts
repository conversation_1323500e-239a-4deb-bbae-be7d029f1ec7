import axios, {
  InternalAxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';

const { PUT_MAP_DATA_ENDPOINT, PUT_MAP_DATA_API_KEY = '' } = process.env;

export const axiosInstance = axios.create({
  baseURL: PUT_MAP_DATA_ENDPOINT,
  timeout: 30000,
  headers: {
    'x-api-key': PUT_MAP_DATA_API_KEY,
    'Content-Type': 'application/json',
  },
});

const onRequest = (
  config: InternalAxiosRequestConfig,
): InternalAxiosRequestConfig => {
  console.info(`[request] [${JSON.stringify(config.data)}]`);
  return config;
};

const onRequestError = (error: AxiosError): Promise<AxiosError> => {
  console.error(`[request error] [${JSON.stringify(error)}]`);
  return Promise.reject(error);
};

const onResponse = (response: AxiosResponse): AxiosResponse => {
  console.info(`[response] [${JSON.stringify(response.data)}]`);
  return response;
};

const onResponseError = (error: AxiosError): Promise<AxiosError> => {
  console.error(`[response error] [${JSON.stringify(error)}]`);
  return Promise.reject(error);
};

axiosInstance.interceptors.request.use(onRequest, onRequestError);
axiosInstance.interceptors.response.use(onResponse, onResponseError);
