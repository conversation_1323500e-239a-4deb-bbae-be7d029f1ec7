{"name": "get-data-semarchy", "private": true, "main": "dist/getDataSemarchy/src/index.js", "scripts": {"prebuild": "npm run clean", "build": "npm i && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "postinstall": "rm -rf ./node_modules/hpagent/test", "start": "ts-node src/run-local.ts", "test": "jest", "test:dev": "jest --watch", "types:check": "tsc --noEmit"}, "dependencies": {"@elastic/elasticsearch": "7.13.0", "axios": "^1.10.0", "dotenv": "^16.0.1", "graphql": "^16.5.0", "graphql-request": "^4.3.0"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/aws-lambda": "^8.10.93", "@types/jest": "^29.5.0", "@types/node": "^17.0.23", "axios-mock-adapter": "^1.21.2", "babel-jest": "^29.5.0", "jest": "^29.5.0", "lambda-local": "^2.0.2", "ts-node": "^10.9.1", "typescript": "^4.6.3"}}