{"name": "retrieve-prices", "private": true, "scripts": {"prebuild": "npm run clean", "build": "npm i && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "start": "ts-node src/run-local.ts", "test": "jest", "types:check": "tsc --noEmit"}, "dependencies": {"aws-lambda": "^1.0.7", "axios": "^1.10.0", "dotenv": "^16.0.3", "ioredis": "^5.2.3"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/aws-lambda": "^8.10.108", "@types/jest": "^29.5.0", "@types/node": "^18.11.5", "babel-jest": "^29.5.0", "jest": "^29.5.0", "lambda-local": "^2.0.3", "prettier": "2.7.1", "ts-node": "^10.9.1", "typescript": "^4.6.3"}}