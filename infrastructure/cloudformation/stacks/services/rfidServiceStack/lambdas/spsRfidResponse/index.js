const { logger } = require('./utils/logger');
const { generateTraceId } = require('./utils/traceId');
const AWS = require('aws-sdk');
const eventBridge = new AWS.EventBridge();
const Client = require('ssh2-sftp-client');
const { put: s3Put } = require('./clients/s3');
const { fetchRFIDDataByCardNumber } = require('./rfid');
const { updateTagInternalStatus } = require('./user');
const { updateKinesis } = require('./kinesis');
const { format } = require('date-fns');
const fs = require('fs');
const { getAvailableCountries } = require('./config');
const traceId = generateTraceId();

const COUNTRIES = {
  GERMANY: 'DE',
  NETHERLANDS: 'NL',
  UNITED_KINGDOM: 'UK',
  UNITED_KINGDOM_SPS: 'GB',
};

const {
  AWS_LOGICAL_ACCOUNT_NAME_UPPER_CASE,
  API_VERSION,
  ENVIRONMENT,
  NODE_ENV,
  RFID_SUPPLIER,
  RFID_SPS_CSV_LOCATION,
  RFID_LOCAL_CSV_LOCATION,
  RFID_BUCKET_NAME,
  RFID_SFTP_HOSTNAME,
  RFID_SFTP_PORT,
  RFID_SFTP_USERNAME,
  RFID_PRIVATE_KEY_REGION,
  RFID_PRIVATE_KEY_LOCATION,
  RFID_RESPONSE_SAFE_TO_DELETE,
  RFID_INCLUDE_TESTFILES,
} = process.env;

if (NODE_ENV === 'production' && !RFID_PRIVATE_KEY_REGION) {
  throw new Error(
    `Could not find RFID_PRIVATE_KEY_REGION env var. Please add env var before executing this lambda!`,
  );
}

// Create a Secrets Manager client
const client = new AWS.SecretsManager({
  region: RFID_PRIVATE_KEY_REGION,
});

const rfidBucketPath = (country, filename) =>
  country && RFID_SUPPLIER
    ? `${country}/${RFID_SUPPLIER}/${format(
        new Date(),
        'yyyyMMdd',
      )}/RESPONSE/${filename}`
    : `${filename}`;

const rfidLocalCsvPath = (filename) =>
  NODE_ENV
    ? `${RFID_LOCAL_CSV_LOCATION}/${filename}`
    : `${__dirname}/download/${filename}`;

const requestedRfidStorageParams = (country, filename, filenamePath) => ({
  rfidBucketName: RFID_BUCKET_NAME || 'rfid-store',
  rfidBucketKey: rfidBucketPath(country, filename),
  rfidBucketBody: filenamePath,
});

const updateTagInternalStatusParams = (
  salesforceId,
  tagCardNumber,
  tagStatus,
  tagSerialNumber,
  country = '',
) => {
  return {
    country,
    salesforceId,
    tagCardNumber,
    tagStatus: tagStatus,
    tagSerialNumber,
  };
};

const updateKinesisParams = (
  tagCardNumber,
  tagSerialNumber,
  userId,
  tagStatus,
  operation,
  country = '',
) => {
  return {
    operation,
    tag_status: tagStatus || 'SUPPLIER_ACCEPTED',
    country,
    user_id: userId,
    tag_serial_number: tagSerialNumber,
    tag_card_number: tagCardNumber,
    tag_type: 'physical',
    expires: '',
  };
};

const spsPrivateKey = () =>
  client
    .getSecretValue({ SecretId: RFID_PRIVATE_KEY_LOCATION })
    .promise()
    .then((data) => {
      return data.SecretString;
    });

const shouldFilterCsvFile = (file) => {
  const isCSVFile = file.name.endsWith('.csv');
  if (!isCSVFile) {
    return false;
  }

  const isTestFile = file.name.endsWith('T.csv');
  const includeTestFiles = RFID_INCLUDE_TESTFILES === 'true';

  return includeTestFiles ? isTestFile : !isTestFile;
};

const eventParams = {
  Name: `${AWS_LOGICAL_ACCOUNT_NAME_UPPER_CASE}-15-Minute-Invocation-Rule-${API_VERSION}-${ENVIRONMENT}`,
  EventBusName: 'default',
};

const getFileCountry = (filename) => {
  switch (true) {
    case filename.includes(COUNTRIES.UNITED_KINGDOM_SPS):
      return COUNTRIES.UNITED_KINGDOM;
    case filename.includes(COUNTRIES.GERMANY):
      return COUNTRIES.GERMANY;
    default:
      return COUNTRIES.GERMANY;
  }
};

const validateCountry = (filename) => {
  const enabledCountries = getAvailableCountries();
  const country = getFileCountry(filename);
  if (!enabledCountries.includes(country)) {
    console.error(
      `Country ${country} is not enabled for RFID processing filename ${filename}`,
    );
  }
  return country;
};

const sftpConnect = async () => {
  const privateKey = NODE_ENV === 'production' ? await spsPrivateKey() : '';
  const sftpConnectParams = {
    host: RFID_SFTP_HOSTNAME || '127.0.0.1',
    port: RFID_SFTP_PORT || '2222',
    username: RFID_SFTP_USERNAME || 'foo',
  };
  const sftpConnectParamsEnhanced = NODE_ENV
    ? {
        ...sftpConnectParams,
        debug: console.log,
        privateKey,
        readyTimeout: 2000,
      }
    : { ...sftpConnectParams, password: '' };
  const sftp = new Client();
  await sftp.connect(sftpConnectParamsEnhanced);
  return sftp;
};

const updateTablesRfid = async (
  tagStatusCode,
  tagCardNumber,
  tagSerialNumber,
  rfidResItem,
  country,
) => {
  if (tagStatusCode) {
    //getting the user id based on the card number
    const dynamoFetchResponse = await fetchRFIDDataByCardNumber({
      cardNumber: tagCardNumber,
      exclusiveStartKey: null,
    }).catch((err) => {
      console.error(
        `Error fetching userID for card Number ${tagCardNumber} from Dynamo: ${err}`,
      );
      throw new Error(
        `fetching userID for card Number ${tagCardNumber} from Dynamo: ${err}`,
      );
    });
    const userId =
      dynamoFetchResponse?.fetchRfidDataByCardNumber?.data[0]?.user_id;
    if (!userId) {
      console.warn(
        'No user_id was found inside the fetched data: ',
        dynamoFetchResponse?.fetchRfidDataByCardNumber?.data[0],
      );
      return;
    }

    // SPS needs countryCode to be GB, but must remain as UK in our logic/DBs
    const spsCountryCode = getCountryCode(country);

    // splitting on each semicolon-separated value per row, we can assign values to our payload for updating Aurora tables via user server
    await updateTagInternalStatus(
      updateTagInternalStatusParams(
        userId,
        tagCardNumber,
        tagStatusCode === '000' ? 'SUPPLIER_ACCEPTED' : 'SUPPLIER_REJECTED',
        tagSerialNumber,
        spsCountryCode,
      ),
    ).catch((err) => {
      console.error(
        `Error updating item ${rfidResItem} in Aurora via User server: ${err}`,
      );
      throw new Error(
        `updating item ${rfidResItem} in Aurora via User server: ${err}`,
      );
    });

    if (tagStatusCode === '000') {
      logger.info('Request by SPS: SUPPLIER_ACCEPTED', {
        componentName: 'sftp-rfid-response',
        traceId: traceId,
        country: spsCountryCode,
        userId: userId,
        cardSerialno: tagSerialNumber,
      });
      await updateKinesis(
        updateKinesisParams(
          tagCardNumber,
          tagSerialNumber,
          userId,
          'ACTIVE',
          'ACTIVATE',
          spsCountryCode,
        ),
      ).catch((err) => {
        console.error(`Error putting item ${rfidResItem} on Kinesis: ${err}`);
        throw new Error(`putting item ${rfidResItem} on Kinesis: ${err}`);
      });
    } else {
      logger.info('Request by SPS: SUPPLIER_REJECTED', {
        componentName: 'sftp-rfid-response',
        traceId: traceId,
        country: spsCountryCode,
        userId: userId,
        cardSerialno: tagSerialNumber,
      });
    }
  }
};

const fetchRfidOrderResponse = async () => {
  console.info(
    `Attempting to transfer file from ${RFID_SPS_CSV_LOCATION} to ${RFID_LOCAL_CSV_LOCATION}`,
  );

  const MAX_RETRIES = 3;
  let retries = 0;
  let sftp = null;

  const connectSftp = async () => {
    while (retries < MAX_RETRIES) {
      try {
        sftp = await sftpConnect();
        return;
      } catch (err) {
        retries++;
        console.warn(
          `SFTP connection failed. Retrying (${retries}/${MAX_RETRIES})...`,
        );
        if (retries >= MAX_RETRIES) {
          throw new Error(
            `Failed to connect to SFTP server after ${MAX_RETRIES} retries: ${err}`,
          );
        }
      }
    }
  };

  try {
    await connectSftp();

    const listOfAllFilenames = await sftp.list(
      RFID_SPS_CSV_LOCATION,
      shouldFilterCsvFile,
    );
    console.log(`List of all filenames: ${JSON.stringify(listOfAllFilenames)}`);

    if (!listOfAllFilenames || listOfAllFilenames.length === 0) {
      const info = `No RFID files found in ${RFID_SPS_CSV_LOCATION}`;
      console.info(`Info: ${info}`);
      return {
        status: 'success',
        reason: info,
      };
    }
    console.log(`List of all filenames: ${JSON.stringify(listOfAllFilenames)}`);

    const file = listOfAllFilenames[0];
    const localFilePath = rfidLocalCsvPath(file.name);
    const remoteFilePath = `${RFID_SPS_CSV_LOCATION}/${file.name}`;
    const fileCountry = validateCountry(file.name);

    console.info(
      `File with name ${file.name} will be processed for fileCountry ${fileCountry}`,
    );

    for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
      try {
        await sftp.get(remoteFilePath, localFilePath);
        break;
      } catch (err) {
        console.error(`Attempt ${attempt + 1} failed: ${err}`);

        if (attempt + 1 >= MAX_RETRIES) {
          return {
            file: file.name,
            status: 'failed',
            reason: `Failed to retrieve CSV file from SFTP server after ${MAX_RETRIES} attempts: ${err}`,
          };
        }
        await connectSftp(); // Reconnect if the attempt fails
      }
    }

    try {
      await s3Put(
        requestedRfidStorageParams(fileCountry, file.name, localFilePath),
      );
    } catch (err) {
      const errorMessage = `Failed to store copy of RFID CSV in S3: ${err}`;
      console.error(errorMessage);
      return {
        file: file.name,
        status: 'failed',
        reason: errorMessage,
      };
    }

    try {
      var fileResult = await processAndUpdateFile(localFilePath, fileCountry);
    } catch (err) {
      const errorMessage = `Failed to process file ${file.name}: ${err}`;
      console.error(errorMessage);
      return {
        file: file.name,
        status: 'failed',
        reason: errorMessage,
      };
    }

    if (RFID_RESPONSE_SAFE_TO_DELETE === 'true') {
      for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
        try {
          console.info(`File with name ${file.name} will be deleted`);
          await sftp.delete(remoteFilePath);
          break;
        } catch (err) {
          console.error(`Attempt ${attempt + 1} to delete file failed: ${err}`);
          if (attempt + 1 >= MAX_RETRIES) {
            return {
              file: file.name,
              status: 'failed',
              reason: `Failed to delete CSV file from SFTP server after ${MAX_RETRIES} attempts: ${err}`,
            };
          }
          await connectSftp(); // Reconnect if the attempt fails
        }
      }
    } else {
      console.info(
        `RFID Delete Feature Flag: ${RFID_RESPONSE_SAFE_TO_DELETE}. SFTP server files will not be deleted`,
      );
    }

    return {
      file: file.name,
      status: 'success',
      data: fileResult,
      remainingFiles: listOfAllFilenames.length - 1,
    };
  } catch (err) {
    const errorMessage = `Failed to connect to SFTP server: ${err}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  } finally {
    sftp && (await sftp.end());
    sftp = null;
  }
};

const processAndUpdateFile = async (localFilePath, country) => {
  const rfidFileData = fs.readFileSync(
    // now read file data for next steps
    localFilePath,
    { encoding: 'utf8' },
  );

  const rfidFileDataArray = rfidFileData.split(/\r?\n/); // creating a array of data by splitting on each new row of csv file
  const rfidRes = rfidFileDataArray.filter((item) => item); // remove null values
  for (const rfidResItem of rfidRes) {
    // an item would look like this: 00001;DEBPE1234567A;04AB0B620D5420;000;25.12.2021
    const [_, tagCardNumber, tagSerialNumber, tagStatusCode, tagDate] =
      rfidResItem.split(';');
    // filter on status ACCEPTED / REJECTED of file fetched from CSV, if there is a status, do the following...
    await updateTablesRfid(
      tagStatusCode,
      tagCardNumber,
      tagSerialNumber,
      rfidResItem,
      country,
    );
  }

  if (!rfidFileDataArray) {
    throw new Error('No RFID file data to return here.');
  }

  // Delete local stored file after processing
  fs.unlink(localFilePath, (err) => {
    if (err) {
      console.error(`Error deleting temporary file ${localFilePath}: ${err}`);
    } else {
      console.info(`Temporary file ${localFilePath} deleted successfully.`);
    }
  });

  return rfidRes;
};

const getCountryCode = (country) => {
  switch (country) {
    case COUNTRIES.UNITED_KINGDOM_SPS:
      return COUNTRIES.UNITED_KINGDOM;
    default:
      return country;
  }
};

exports.handler = async () => {
  try {
    const results = [];
    try {
      const fileResult = await fetchRfidOrderResponse();
      if (fileResult.status === 'success') {
        results.push(fileResult);
      } else {
        console.error(
          `Failure processing file ${fileResult.file}: ${fileResult.reason}`,
        );
        results.push(
          new Error(
            `Failure processing file ${fileResult.file}: ${fileResult.reason}`,
          ),
        );
      }

      if (
        RFID_RESPONSE_SAFE_TO_DELETE === 'true' &&
        fileResult.remainingFiles > 0
      ) {
        console.info(
          `There are still ${fileResult.remainingFiles} remaining files. Retriggering Lambda.`,
        );

        const ruleDescription = await eventBridge
          .describeRule(eventParams)
          .promise();

        if (ruleDescription.State === 'DISABLED') {
          await eventBridge.enableRule(eventParams).promise();
          console.info(`EventBridge rule ${eventParams.Name} is enabled: `);
        }
        console.info(
          'Lambda function scheduled to be invoked after 15 minutes. ',
        );
      } else {
        // Call EventBridge to disable the rule
        await eventBridge.disableRule(eventParams).promise();

        console.info(
          `EventBridge rule '${eventParams.Name}' disabled successfully`,
        );
      }
    } catch (err) {
      // Disable retriggering of lambda if error occur
      await eventBridge.disableRule(eventParams).promise();

      console.error(`Failure executing RFID response lambda: ${err}`);
      results.push(new Error(`Failure executing RFID response lambda: ${err}`));
    }
    return results;
  } finally {
    console.info('Successfully executed RFID response lambda.');
  }
};
