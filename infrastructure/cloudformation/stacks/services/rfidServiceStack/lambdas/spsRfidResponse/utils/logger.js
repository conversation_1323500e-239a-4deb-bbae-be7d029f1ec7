const { createLogger, format, transports } = require('winston');

// Handle formatting splat data of log
const formatSplat = (splat) =>
  splat.length === 1 ? JSON.stringify(splat[0]) : JSON.stringify(splat);

const customFormat = format.printf(({ timestamp, level, message, ...meta }) => {
  const splat = meta[Symbol.for('splat')];
  const componentName = meta?.componentName || 'no-component-name';
  const traceId = meta?.traceId || 'no-trace-id';
  const country = meta?.country || 'no country found';
  const userId = meta?.userId || 'no userid found';
  const cardSerialno = meta?.cardSerialno || 'no-card-serialid';

  return `${timestamp} - ${level.toUpperCase()} - ${componentName} - ${traceId} - ${country} - ${userId} - ${cardSerialno}- ${message} ${
    (splat && formatSplat(splat)) || ''
  }`;
});

// Initialise instance of winston logger
const logger = createLogger({
  level: 'info', // default level
  format: format.combine(
    format.timestamp({ format: 'YYYY-MM-DDTHH:mm:ss.SSSZ' }),
    customFormat,
  ),
  transports: [new transports.Console()],
});

const logAndThrowError = (errorMessage) => {
  logger.error(`🚨 Error encountered:`, { errorMessage });
  throw new Error(errorMessage);
};

module.exports = {
  logger,
  logAndThrowError,
};
