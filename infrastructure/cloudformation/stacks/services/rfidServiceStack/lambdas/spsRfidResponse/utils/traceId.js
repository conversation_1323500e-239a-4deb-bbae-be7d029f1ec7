const { IncomingHttpHeaders } = require('http');
const { v4 } = require('uuid');

/**
 * Creates a trace ID attempting to use the identifier that will provide the most log coverage
 * Example x-amzn-apigw-request-id: ce54e310-863c-11e9-ba01-2f74f1242d3b
 * Example amzn-trace-id: Root=1-63f6e7d3-3d32e0492e641b7f564f1a5d;Parent=256dd58e3716b51a;Sampled=0
 * @param headers request headers
 * @returns request trace Id string
 */
const generateTraceId = (headers = {}) =>
  // Existing log trace ID passed from request source
  headers['x-log-trace-id']?.toString() ||
  // Custom header passing the API Gateway request Id, included in all request logs in API Gateway
  headers['x-amzn-apigw-request-id']?.toString() ||
  // AWS tracing header, not present in all logs
  headers['x-amzn-trace-id']?.toString().split('Root=')[1]?.split(';')[0] ||
  // UUID v4, used when a request is made internally, service-to-service, or local development
  v4();

module.exports = {
  generateTraceId,
};
