import * as lambda from './index';
import {
  MockHistoryRecord,
  MockHistoryRecordWithPayback,
  MockPaymentRecord,
  MockPreAuthPaymentRecord,
} from './mockDataObjects';
import { PaymentType } from './types';

jest.mock('./env');

const mockProcessOutstandingPayment = jest.fn();
const mockGetHistoryRecord = jest.fn();
const mockHandlePayment = jest.fn();
jest.mock('./service.functions', () => ({
  getHistoryRecordData: () => mockGetHistoryRecord(),
  processOutstandingPayment: () => mockProcessOutstandingPayment(),
  applyDiscount: jest.fn(),
  handleFullDiscount: () => jest.fn(),
  handlePayment: () => mockHandlePayment(),
}));

const mockMakeSimplePayment = jest.fn();
const mockGetPaymentRecord = jest.fn();
const mockVoidOrder = jest.fn();
jest.mock('./services/bppay', () => ({
  makeSimplePayment: () => mockMakeSimplePayment(),
  getPaymentRecord: () => mockGetPaymentRecord(),
  voidOrder: () => mockVoidOrder(),
}));

const mockUpdateHistoryRecord = jest.fn();
jest.mock('./services/history', () => ({
  updateHistoryRecord: () => mockUpdateHistoryRecord(),
}));

const mockGetUserInfoData = jest.fn();
jest.mock('./services/user', () => ({
  getUserInfoData: () => mockGetUserInfoData(),
}));

const mockCollectLoyaltyPoints = jest.fn();
jest.mock('./services/voucher', () => ({
  collectLoyaltyPoints: () => mockCollectLoyaltyPoints(),
}));

jest.spyOn(global.console, 'info').mockImplementation(jest.fn());
jest.spyOn(global.console, 'error').mockImplementation(jest.fn());

const mockEvent = {
  chargeSessionId: 'AMUK-1234-1234-1234',
  paymentId: 'payment-1',
};
mockGetHistoryRecord.mockResolvedValue(MockHistoryRecord);
mockUpdateHistoryRecord.mockResolvedValue({
  status: '200',
  message: 'message',
});
mockGetPaymentRecord.mockResolvedValue(MockPaymentRecord);
mockVoidOrder.mockResolvedValue({ status: 200, message: 'success' });
mockHandlePayment.mockResolvedValue({
  status: 200,
  paymentType: PaymentType.MIT,
});
mockProcessOutstandingPayment.mockResolvedValue(undefined);
mockGetUserInfoData.mockResolvedValue(undefined);
mockCollectLoyaltyPoints.mockResolvedValue(undefined);

describe('Registered Charge Process Lambda Tests', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should throw if it does not receive history record data', async () => {
    expect.assertions(1);
    mockGetHistoryRecord.mockRejectedValueOnce(
      new Error('getHistoryRecord error'),
    );
    await expect(lambda.handler(mockEvent)).rejects.toThrow(`
      {
        status: 500,
        message: getHistoryRecord error
      }`);
  });

  it('should throw if it does not receive app country', async () => {
    expect.assertions(1);
    mockGetHistoryRecord.mockResolvedValueOnce({
      ...MockHistoryRecord,
      homeCountry: undefined,
    });
    await expect(
      lambda.handler({ ...mockEvent, chargeSessionId: 'AMXX-1234-1234-1234' }),
    ).rejects.toThrow(`
      {
        status: 500,
        message: Missing userHomeCountry.
      }`);
  });

  it('should throw if it does not receive payment record data', async () => {
    expect.assertions(1);
    mockGetPaymentRecord.mockRejectedValueOnce(
      new Error('getPaymentRecord error'),
    );
    await expect(lambda.handler(mockEvent)).rejects.toThrow(`
      {
        status: 500,
        message: getPaymentRecord error
      }`);
  });

  it('should not fetch payment record data if paymentId is an empty string', async () => {
    expect.assertions(1);
    await lambda.handler({ ...mockEvent, paymentId: '' });
    expect(mockGetPaymentRecord).not.toHaveBeenCalled();
  });

  it('should exit when total gross is 0', async () => {
    mockGetHistoryRecord.mockResolvedValueOnce({
      ...MockHistoryRecord,
      totalGross: 0,
    });
    await lambda.handler(mockEvent);
    expect(console.info).toHaveBeenCalledWith(
      `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:${mockEvent.chargeSessionId} - Total gross is 0, exiting`,
    );
  });

  it('should void preauth and exit when total gross is 0', async () => {
    mockGetHistoryRecord.mockResolvedValueOnce({
      ...MockHistoryRecord,
      totalGross: 0,
    });
    mockGetPaymentRecord.mockResolvedValueOnce(MockPreAuthPaymentRecord);
    await lambda.handler(mockEvent);
    expect(console.info).toHaveBeenNthCalledWith(
      3,
      `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:${mockEvent.chargeSessionId} - Voiding pre-auth`,
    );
    expect(mockVoidOrder).toHaveBeenCalled();
    expect(mockUpdateHistoryRecord).toHaveBeenCalled();
    expect(console.info).toHaveBeenNthCalledWith(
      4,
      `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:${mockEvent.chargeSessionId} - Total gross is 0, exiting`,
    );
  });

  it('should process outstanding payment when handle payment ends with non-200 status', async () => {
    mockHandlePayment.mockResolvedValueOnce({
      status: 400,
      reason: 'error',
      paymentType: PaymentType.MIT,
    });
    const result = await lambda.handler(mockEvent);
    expect(console.error).toHaveBeenCalledWith(
      `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:${mockEvent.chargeSessionId} - Failed to make a payment.`,
      {
        type: 'MIT',
        status: 400,
        reason: 'error',
      },
    );
    expect(mockProcessOutstandingPayment).toHaveBeenCalled();
    expect(result).toEqual({
      status: 400,
      message: 'Failed to make a payment',
    });
  });

  it('should log failure to collect loyalty points and return 200 when paybackId exists', async () => {
    mockGetHistoryRecord.mockResolvedValueOnce(MockHistoryRecordWithPayback);
    mockCollectLoyaltyPoints.mockRejectedValueOnce(
      new Error('collectLoyaltyPoints error'),
    );
    const response = await lambda.handler(mockEvent);
    expect(mockCollectLoyaltyPoints).toHaveBeenCalled();
    expect(console.error).toHaveBeenCalledWith(
      `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:${mockEvent.chargeSessionId} - Failed to collect loyalty points for userId 12345 : collectLoyaltyPoints error`,
      new Error('collectLoyaltyPoints error'),
    );
    expect(response).toEqual({
      status: 200,
      message: 'Registered Charge Processing Completed',
    });
  });

  it('should collect loyalty points and return 200 when paybackId exists', async () => {
    mockGetHistoryRecord.mockResolvedValueOnce(MockHistoryRecordWithPayback);
    const response = await lambda.handler(mockEvent);
    expect(mockCollectLoyaltyPoints).toHaveBeenCalled();
    expect(response).toEqual({
      status: 200,
      message: 'Registered Charge Processing Completed',
    });
  });

  it('should return 200 when all succeeds', async () => {
    const response = await lambda.handler(mockEvent);
    expect(response).toEqual({
      status: 200,
      message: 'Registered Charge Processing Completed',
    });
  });
});
