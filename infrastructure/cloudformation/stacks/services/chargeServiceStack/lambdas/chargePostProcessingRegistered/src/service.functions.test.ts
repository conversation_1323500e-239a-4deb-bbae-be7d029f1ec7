import { MockCdrWithPayback } from './mockDataObjects';
import {
  applyDiscount,
  getHistoryRecordData,
  handlePayment,
  processOutstandingPayment,
} from './service.functions';
import * as OfferService from './services/offer/api';
import { OfferStatus } from './services/offer/gql-client/graphql';
import { PaymentAuthType } from './services/user/gql-client/graphql';
import { AppType, PaymentType } from './types';

const mockGetHistoryRecord = jest.fn();
const mockUpdateHistoryRecord = jest.fn();
const mockCapturePayment = jest.fn();
const mockCreateOrder = jest.fn();
const mockCreatePaymentRecord = jest.fn();
const mockVoidOrder = jest.fn();
const mockUpdatePaymentStatus = jest.fn();
const mockCollectLoyaltyPoints = jest.fn();
const mockUpdateUserInternal = jest.fn();
const mockBlockRfid = jest.fn();
const mockGetTaginfo = jest.fn();
const mockGetDefaultPayment = jest.fn();
const mockMakeSimplePayment = jest.fn();

jest.mock('./env');

jest.mock('uuid', () => ({ v4: () => '7c5e8e94-a2a3-4556-aeea-385f8159d3bd' }));

jest.mock('./services/bppay', () => ({
  makeSimplePayment: () => mockMakeSimplePayment(),
  updatePaymentStatus: () => mockUpdatePaymentStatus(),
  capturePayment: () => mockCapturePayment(),
  createOrder: () => mockCreateOrder(),
  createPaymentRecord: () => mockCreatePaymentRecord(),
  voidOrder: () => mockVoidOrder(),
}));

jest.mock('./services/wallet', () => ({
  getDefaultPaymentMethodId: () => mockGetDefaultPayment(),
}));

jest.mock('./services/history', () => ({
  getHistoryRecord: () => mockGetHistoryRecord(),
  updateHistoryRecord: (...args: Array<unknown>) =>
    mockUpdateHistoryRecord(...args),
}));

jest.mock('./services/voucher', () => ({
  collectLoyaltyPoints: () => mockCollectLoyaltyPoints(),
}));

jest.mock('./services/rfid', () => ({
  blockRfid: (args: any[]) => mockBlockRfid(args),
}));

jest.mock('./services/user', () => ({
  getTagInfo: () => mockGetTaginfo(),
  updateUserInternal: () => mockUpdateUserInternal(),
}));

jest.mock('./utils/requests', () => ({
  pushNotifications: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('./utils/helpers', () => ({
  ...jest.requireActual('./utils/helpers'),
  getAppCountry: jest.fn().mockReturnValue('DE'),
}));

const getOffersByUserSpy = jest.spyOn(OfferService, 'getOffersByUser');
const updateOfferCreditBalanceSpy = jest.spyOn(
  OfferService,
  'updateOfferCreditBalance',
);

jest.spyOn(global.console, 'log').mockImplementation(jest.fn());
jest.spyOn(global.console, 'error').mockImplementation(jest.fn());
jest.spyOn(global.console, 'info').mockImplementation(jest.fn());

describe('Service Function Tests', () => {
  afterEach(() => jest.clearAllMocks());

  describe('getHistoryRecordData()', () => {
    it('Should return correct history record payload', async () => {
      mockGetHistoryRecord.mockImplementation(() => MockCdrWithPayback);

      const chargeSessionId = 'ARDE-1234-1234-1234';

      const response = await getHistoryRecordData(chargeSessionId);

      expect(response).toStrictEqual({
        currency: 'EUR',
        homeCountry: 'UK',
        isStandardInvoice: true,
        paybackId: '123456',
        receivedTagId: '048955AA565F87',
        totalGross: 1.5,
        userId: '12345',
      });
    });
  });

  describe('processOutstandingPayment()', () => {
    const userId = '12345';
    const chargeSessionId = 'ARDE-1234-1234-1234';
    const receivedTagId = '048955AA565F87';
    const userInfo = {
      type: 'PAYG',
      tagIds: [
        {
          tagId: 'tagId-mobile',
          tagCardNumber: 'tagCardNumber-mobile',
          tagNotes: 'virtual-RFID',
          tagStatus: 'active',
        },
      ],
    };

    it('Should block rfid when app type is rfid and has tagIds', async () => {
      mockGetTaginfo.mockResolvedValue({ tagId: '', tagCardNumber: '' });

      await processOutstandingPayment({
        chargeSessionId,
        userId,
        userInfo,
        receivedTagId,
      });
      expect(mockBlockRfid).toHaveBeenCalledTimes(1);
    });

    it('Should not block rfid when no tagIds', async () => {
      mockGetTaginfo.mockImplementation(() => undefined);

      await processOutstandingPayment({
        chargeSessionId,
        userId,
        userInfo,
        receivedTagId,
      });

      expect(mockBlockRfid).toHaveBeenCalledTimes(0);
    });

    it('should skip the user balance update if we are unable to get the existing balance of the user', async () => {
      const result = processOutstandingPayment({
        chargeSessionId,
        userId,
        userInfo: undefined,
        receivedTagId,
      });

      await expect(result).rejects.toThrow(
        'Missing userInfo, cannot check for physical RFID tag',
      );
      expect(mockUpdateUserInternal).toHaveBeenCalledTimes(0);
    });

    it('should block rfid for mobile charge', async () => {
      await processOutstandingPayment({
        chargeSessionId: 'AMUK-1234-1234-1234',
        userId,
        userInfo: {
          tagIds: [
            {
              tagId: 'tagId-mobile',
              tagCardNumber: 'tagCardNumber-mobile',
              tagNotes: 'physical-RFID',
              tagStatus: 'active',
            },
          ],
        },
        receivedTagId,
      });
      expect(mockBlockRfid).toHaveBeenCalledWith({
        userId,
        reasonForBlocking: 'No Credit',
        cardUid: 'tagId-mobile',
        cardNumber: 'tagCardNumber-mobile',
        country: 'DE',
      });
      expect(console.log).toHaveBeenCalledWith(
        'Physical RFID tag found and active, blocking card tagCardNumber-mobile',
      );
    });

    it('should not block rfid for mobile charge if card is inactive', async () => {
      await processOutstandingPayment({
        chargeSessionId: 'AMUK-1234-1234-1234',
        userId,
        userInfo,
        receivedTagId,
      });
      expect(mockBlockRfid).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith(
        'No physical RFID tag found for Mobile charge',
      );
    });

    it('should not block rfid for mobile charge if no physical-RFID card is present', async () => {
      await processOutstandingPayment({
        chargeSessionId: 'AMUK-1234-1234-1234',
        userId,
        userInfo,
        receivedTagId,
      });
      expect(mockBlockRfid).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith(
        'No physical RFID tag found for Mobile charge',
      );
    });
  });

  describe('applyDiscount()', () => {
    const offer1 = {
      offerCode: 'offer-1',
      creditBalance: 6,
      creditStatus: OfferStatus.REDEEMED,
    };

    const offer2 = {
      offerCode: 'offer-2',
      creditBalance: 3,
      creditStatus: OfferStatus.REDEEMED,
    };

    const offer3 = {
      offerCode: 'offer-3',
      creditBalance: 0,
      creditStatus: OfferStatus.REDEEMED,
    };

    mockUpdateHistoryRecord.mockResolvedValue({
      status: '200',
      message: 'message',
    });

    it('should apply discount for UK users', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer1] });
      updateOfferCreditBalanceSpy.mockResolvedValueOnce({});

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 10);
      expect(result).toEqual(4);
    });

    it('should fix floating point precision errors', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer2] });
      updateOfferCreditBalanceSpy.mockResolvedValueOnce({});

      const result = await applyDiscount(
        'chargeSessionId',
        'user-1',
        'UK',
        4.1,
      );
      expect(result).toEqual(1.1);
    });

    it('should NOT apply discount for non-UK users', async () => {
      expect.assertions(3);

      const result = await applyDiscount('chargeSessionId', 'user-1', 'NL', 10);
      expect(result).toEqual(10);

      expect(getOffersByUserSpy).toHaveBeenCalledTimes(0);
      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledTimes(0);
    });

    it('should update credit balance', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer1] });
      updateOfferCreditBalanceSpy.mockResolvedValueOnce({});

      await applyDiscount('chargeSessionId', 'user-1', 'UK', 10);
      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledWith('offer-1', 6);
    });

    it('should apply discount partially when balance is greater than total gross', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer1] });
      updateOfferCreditBalanceSpy.mockResolvedValueOnce({});

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 2);
      expect(result).toEqual(0);
      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledWith('offer-1', 2);
    });

    it('should apply multiple discounts when balance is less than total gross', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer1, offer2] });
      updateOfferCreditBalanceSpy.mockResolvedValue({});

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 10);
      expect(result).toEqual(1);

      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledTimes(2);

      const call1 = updateOfferCreditBalanceSpy.mock.calls[0];
      const call2 = updateOfferCreditBalanceSpy.mock.calls[1];

      expect(call1).toEqual(['offer-1', 6]);
      expect(call2).toEqual(['offer-2', 3]);
    });

    it('should update history record when applying discount', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer1] });
      updateOfferCreditBalanceSpy.mockResolvedValueOnce({});

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 10);
      expect(mockUpdateHistoryRecord).toHaveBeenCalledWith({
        chargeSessionId: 'chargeSessionId',
        discount: 6,
        offerCodes: [
          { offerCode: offer1.offerCode, offerValue: offer1.creditBalance },
        ],
      });
      expect(result).toEqual(4);
    });

    it('should skip offer when it has no balance', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer2, offer3] });
      updateOfferCreditBalanceSpy.mockResolvedValue({});

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 10);
      expect(result).toEqual(7);

      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledTimes(1);
      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledWith('offer-2', 3);
    });

    it('should return total gross when user has no offers', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [] });

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 5);
      expect(result).toEqual(5);

      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledTimes(0);
    });

    it('should return total gross on fail to get offers for user', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ error: 'error' });

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 5);
      expect(result).toEqual(5);

      expect(console.error).toHaveBeenCalledWith(
        '[ALARM - ApplyDiscount] Failed to fetch user offers due: error',
      );
      expect(updateOfferCreditBalanceSpy).toHaveBeenCalledTimes(0);
    });

    it('should return total gross on failure', async () => {
      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 5);
      expect(result).toEqual(5);
    }, 15000);

    it('should return total gross on failure to update history record', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({ offers: [offer2, offer3] });
      updateOfferCreditBalanceSpy.mockResolvedValue({});
      mockUpdateHistoryRecord.mockResolvedValueOnce({
        status: '400',
        message: 'error',
      });

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 5);
      expect(console.error).toHaveBeenCalledWith(
        '[ALARM - ApplyDiscount] Failed to update history record with discount due: error',
      );
      expect(result).toEqual(5);
    });

    it('should only include offers with redeemed creditStatus', async () => {
      getOffersByUserSpy.mockResolvedValueOnce({
        offers: [{ ...offer1, creditStatus: OfferStatus.USED }],
      });

      const result = await applyDiscount('chargeSessionId', 'user-1', 'UK', 5);

      expect(mockUpdateHistoryRecord).not.toHaveBeenCalled();
      expect(result).toEqual(5);
    });
  });

  describe('handlePayment()', () => {
    describe('handleFullDiscount()', () => {
      const args = {
        amount: 0,
        appType: AppType.Mobile,
        chargeSessionId: 'chargeSessionId',
        currency: 'GBP',
        eventPaymentId: 'eventPaymentId',
        hasPreAuth: undefined,
        userId: 'userId',
        userHomeCountry: 'UK',
        userType: undefined,
        paymentAuthType: PaymentAuthType.MIT,
        preAuthAmount: 0,
        preAuthStatus: false,
        voidTransaction: false,
      };

      it('should trigger new paymentId creation for RFID user', async () => {
        mockCreatePaymentRecord.mockResolvedValueOnce({
          createPaymentRecordInternal: true,
        });
        const response = await handlePayment({
          ...args,
          appType: AppType.RFID,
        });
        expect(console.info).toHaveBeenNthCalledWith(
          1,
          'charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - RFID session for fully discounted charge with new paymentRecordId: 7c5e8e94-a2a3-4556-aeea-385f8159d3bd',
        );
        expect(response).toEqual({
          status: 200,
          paymentType: PaymentType.FULL_DISCOUNT,
        });
      });

      it('should void order and log error when it fails to create payment record for charge with preAuth due to server error', async () => {
        mockCreatePaymentRecord.mockRejectedValueOnce(
          new Error('server error'),
        );
        const response = await handlePayment({ ...args, hasPreAuth: true });
        expect(mockVoidOrder).toHaveBeenCalled();
        expect(console.error).toHaveBeenCalledWith(
          `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Failed to create payment record for userId userId : server error`,
          new Error('server error'),
        );
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.FULL_DISCOUNT,
          reason: 'server error',
        });
      });

      it('should void order and log error when create payment record for charge with preAuth endpoint returns false', async () => {
        mockCreatePaymentRecord.mockResolvedValueOnce({
          createPaymentRecordInternal: false,
        });
        const response = await handlePayment({ ...args, hasPreAuth: true });
        expect(mockVoidOrder).toHaveBeenCalled();
        expect(console.error).toHaveBeenCalledWith(
          `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Failed to create payment record for userId userId : undefined`,
          null,
        );
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.FULL_DISCOUNT,
        });
      });

      it('should void order and log success when create payment record succeeds for charge with preAuth', async () => {
        mockCreatePaymentRecord.mockResolvedValueOnce({
          createPaymentRecordInternal: true,
        });
        const response = await handlePayment({ ...args, hasPreAuth: true });
        expect(mockVoidOrder).toHaveBeenCalled();
        expect(console.info).not.toHaveBeenCalledWith(
          'charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Created payment record for userId userId',
        );
        expect(response).toEqual({
          status: 200,
          paymentType: PaymentType.FULL_DISCOUNT,
        });
      });

      it('should log error when it fails to create payment record due to server error', async () => {
        mockCreatePaymentRecord.mockRejectedValueOnce(
          new Error('server error'),
        );
        const response = await handlePayment(args);
        expect(console.error).toHaveBeenCalledWith(
          `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Failed to create payment record for userId userId : server error`,
          new Error('server error'),
        );
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.FULL_DISCOUNT,
          reason: 'server error',
        });
      });

      it('should log error when create payment record endpoint returns false', async () => {
        mockCreatePaymentRecord.mockResolvedValueOnce({
          createPaymentRecordInternal: false,
        });
        const response = await handlePayment(args);
        expect(console.error).toHaveBeenCalledWith(
          `charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Failed to create payment record for userId userId : undefined`,
          null,
        );
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.FULL_DISCOUNT,
        });
      });

      it('should log success when create payment record succeeds', async () => {
        mockCreatePaymentRecord.mockResolvedValueOnce({
          createPaymentRecordInternal: true,
        });
        const response = await handlePayment(args);
        expect(console.info).toHaveBeenCalledWith(
          'charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Created an empty payment record for userId userId.',
        );
        expect(response).toEqual({
          status: 200,
          paymentType: PaymentType.FULL_DISCOUNT,
        });
      });
    });

    describe('handlePreAuthPayment()', () => {
      const args = {
        appType: AppType.Mobile,
        amount: 31.59,
        hasPreAuth: true,
        eventPaymentId: 'eventPaymentId',
        chargeSessionId: 'chargeSessionId',
        userId: 'userId',
        userHomeCountry: 'UK',
        userType: undefined,
        currency: 'GBP',
        paymentAuthType: PaymentAuthType.PREAUTH,
        preAuthAmount: 50,
        preAuthStatus: true,
        voidTransaction: false,
      };

      it('should void order and return status 500 and reason when it fails to capture payment due to server error', async () => {
        mockCapturePayment.mockRejectedValueOnce(new Error('server error'));
        const response = await handlePayment(args);
        expect(mockVoidOrder).toHaveBeenCalled();
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.PREAUTH,
          reason: 'server error',
        });
      });

      it('should void order and return status 500 and when capture payment endpoint does not return status', async () => {
        mockCapturePayment.mockResolvedValueOnce({ status: undefined });
        const response = await handlePayment(args);
        expect(mockVoidOrder).toHaveBeenCalled();
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.PREAUTH,
        });
      });

      it('should return status 200 and when capture payment endpoint succeeds', async () => {
        mockCapturePayment.mockResolvedValueOnce({ status: 200 });
        const response = await handlePayment(args);
        expect(response).toEqual({
          status: 200,
          paymentType: PaymentType.PREAUTH,
        });
      });
    });

    describe('handleMITPayment()', () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });
      const args = {
        appType: AppType.Mobile,
        amount: 31.59,
        hasPreAuth: false,
        eventPaymentId: 'eventPaymentId',
        chargeSessionId: 'chargeSessionId',
        userId: 'userId',
        userHomeCountry: 'UK',
        userType: undefined,
        currency: 'GBP',
        paymentAuthType: PaymentAuthType.MIT,
        preAuthAmount: 50,
        preAuthStatus: true,
        voidTransaction: false,
      };

      it('should log error when running MIT payment for a user with PreAuth paymentAuthType.', async () => {
        mockGetDefaultPayment.mockRejectedValueOnce(
          new Error('default payment method server error'),
        );
        await handlePayment({
          ...args,
          paymentAuthType: PaymentAuthType.PREAUTH,
        });
        expect(console.error).toHaveBeenCalledWith(
          'charge-service.chargePostProcessingRegistered lambda - chargeSessionId:chargeSessionId - Expected PREAUTH payment but making simple payment instead. User: userId, country: UK, appType: Mobile, preAuthStatus: true, voidTransaction: false',
        );
      });

      it('should not log error when running MIT payment for a user with MIT paymentAuthType.', async () => {
        mockGetDefaultPayment.mockRejectedValueOnce(
          new Error('default payment method server error'),
        );
        await handlePayment({ ...args, userHomeCountry: 'DE' });
        expect(console.error).not.toHaveBeenCalled();
      });

      it('should return status 500 and reason when it fails to get default payment method due to server error', async () => {
        mockGetDefaultPayment.mockRejectedValueOnce(
          new Error('default payment method server error'),
        );
        const response = await handlePayment(args);
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.MIT,
          reason: 'default payment method server error',
        });
      });

      it('should return status 500 and reason when it fails to createOrder due to server error', async () => {
        mockGetDefaultPayment.mockResolvedValueOnce({ paymentId: 'paymentId' });
        mockCreateOrder.mockRejectedValueOnce(
          new Error('createOrder server error'),
        );
        const response = await handlePayment(args);
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.MIT,
          reason: 'createOrder server error',
        });
      });

      it('should return status 500 and reason when it fails to makeSimplePayment due to server error', async () => {
        mockGetDefaultPayment.mockResolvedValueOnce({ paymentId: 'paymentId' });
        mockCreateOrder.mockResolvedValueOnce({
          correlationId: 'correlationId',
          paymentId: 'paymentId',
        });
        mockMakeSimplePayment.mockRejectedValueOnce(
          new Error('makeSimplePayment server error'),
        );
        const response = await handlePayment(args);
        expect(response).toEqual({
          status: 500,
          paymentType: PaymentType.MIT,
          reason: 'makeSimplePayment server error',
        });
      });

      it('should return status 200 when it succeeds to makeSimplePayment', async () => {
        mockGetDefaultPayment.mockResolvedValueOnce({ paymentId: 'paymentId' });
        mockCreateOrder.mockResolvedValueOnce({
          correlationId: 'correlationId',
          paymentId: 'paymentId',
        });
        mockMakeSimplePayment.mockResolvedValueOnce({
          status: 200,
        });
        const response = await handlePayment(args);
        expect(response).toEqual({
          status: 200,
          paymentType: PaymentType.MIT,
        });
      });
    });
  });
});
