import xss from 'xss';
import {
  applyDiscount,
  getHistoryRecordData,
  handlePayment,
  processOutstandingPayment,
} from './service.functions';
import { getPaymentRecord, voidOrder } from './services/bppay';
import { updateHistoryRecord } from './services/history';
import { getUserInfoData } from './services/user';
import { collectLoyaltyPoints } from './services/voucher';
import { AppType, PaymentStatus, PaymentType } from './types';
import { attempt } from './utils/attempt';
import { LOG_PREFIX } from './utils/constants';
import { getAppType } from './utils/helpers';

export type ChargePostProcessingRegisteredEvent = {
  chargeSessionId: string;
  paymentId: string;
};

export const handler = async ({
  chargeSessionId: chargeSessionIdUnsanitised,
  paymentId: paymentIdUnsanitised,
}: ChargePostProcessingRegisteredEvent) => {
  const chargeSessionId = xss(chargeSessionIdUnsanitised);
  const eventPaymentId = xss(paymentIdUnsanitised);
  try {
    console.info(
      `${LOG_PREFIX}:${chargeSessionId} - Lambda triggered with paymentId: ${eventPaymentId}`,
    );

    // 1 Get history and payment records. Initialise variables.
    const {
      userId,
      totalGross,
      currency,
      paybackId,
      receivedTagId,
      homeCountry,
    } = await getHistoryRecordData(chargeSessionId);
    if (!userId || typeof totalGross !== 'number' || !currency) {
      const errorMessage =
        'Incomplete or empty data received from history record.';
      console.error(`${LOG_PREFIX}:${chargeSessionId} - ${errorMessage}`);
      throw new Error(errorMessage);
    }
    console.info(
      `${LOG_PREFIX}:${chargeSessionId} - Data retrieved from history record userID: ${userId}, totalGross: ${totalGross}, currency: ${currency}, paybackId: ${paybackId}, tagId: ${receivedTagId}, homeCountry: ${homeCountry}`,
    );

    const [userInfo, userInfoError] = await attempt(
      getUserInfoData({ userId }),
    );
    if (userInfoError) {
      console.error(
        `${LOG_PREFIX}:${chargeSessionId} - Failed to get user info for userID: ${userId}. Continuing execution.`,
      );
    }
    const userHomeCountry = userInfo?.country || homeCountry;
    if (!userHomeCountry) {
      const errorMessage = 'Missing userHomeCountry.';
      console.error(`${LOG_PREFIX}:${chargeSessionId} - ${errorMessage}`);
      throw new Error(errorMessage);
    }
    const appType = getAppType(chargeSessionId);
    const userType = userInfo?.type ?? undefined;

    let preAuthAmount: number | undefined;
    let preAuthStatus: boolean | undefined;
    let voidTransaction: boolean | undefined;
    let hasPreAuth = false;

    // Ensure payment data is retrieved only when a valid paymentId is provided
    if (eventPaymentId?.trim()) {
      const paymentRecord = await getPaymentRecord(eventPaymentId);

      preAuthAmount = paymentRecord?.preAuthAmount ?? undefined;
      preAuthStatus = paymentRecord?.preAuthStatus ?? undefined;
      voidTransaction = paymentRecord?.voidTransaction ?? undefined;
      hasPreAuth =
        (preAuthStatus && !voidTransaction && appType === AppType.Mobile) ??
        false;
    }

    // 2 Exit (and void preAuth) when totalGross is 0
    if (totalGross === 0) {
      if (hasPreAuth) {
        console.info(`${LOG_PREFIX}:${chargeSessionId} - Voiding pre-auth`);
        await voidOrder([
          { paymentId: eventPaymentId, country: userHomeCountry },
        ]);
        await updateHistoryRecord({
          chargeSessionId,
          paymentStatus: PaymentStatus.CAPTURED,
          paymentId: eventPaymentId,
        });
      }
      console.info(
        `${LOG_PREFIX}:${chargeSessionId} - Total gross is 0, exiting`,
      );
      return;
    }

    // 3 Handle discounts
    const amount = await applyDiscount(
      chargeSessionId,
      userId,
      userHomeCountry,
      totalGross,
    );
    console.info(
      `${LOG_PREFIX}:${chargeSessionId} - Gross amount after discount: ${amount}`,
    );

    // 4 Handle payment
    const paymentResult = await handlePayment({
      appType,
      amount,
      chargeSessionId,
      currency,
      eventPaymentId,
      hasPreAuth,
      paymentAuthType: userInfo?.paymentAuthType,
      preAuthStatus,
      preAuthAmount,
      userHomeCountry,
      userId,
      userType,
      voidTransaction,
    });

    // 5 Handle failed payment
    if (
      paymentResult.status !== 200 &&
      paymentResult.paymentType !== PaymentType.FULL_DISCOUNT
    ) {
      console.error(
        `${LOG_PREFIX}:${chargeSessionId} - Failed to make a payment.`,
        {
          type: paymentResult.paymentType,
          status: paymentResult.status,
          reason: paymentResult.reason,
        },
      );
      await processOutstandingPayment({
        chargeSessionId,
        userId,
        userInfo,
        receivedTagId: receivedTagId ?? '',
      });
      return {
        status: paymentResult.status,
        message: 'Failed to make a payment',
      };
    }

    // 6 Collect loyalty points
    if (paybackId) {
      console.info(
        `${LOG_PREFIX}:${chargeSessionId} - Collecting loyalty points for userId ${userId}...`,
      );
      const [, collectLoyaltyPointsError] = await attempt(
        collectLoyaltyPoints(chargeSessionId),
      );
      if (collectLoyaltyPointsError) {
        console.error(
          `${LOG_PREFIX}:${chargeSessionId} - Failed to collect loyalty points for userId ${userId} : ${collectLoyaltyPointsError.message}`,
          collectLoyaltyPointsError,
        );
      }
    }

    return {
      status: 200,
      message: 'Registered Charge Processing Completed',
    };
  } catch (e) {
    const err = e as Error;
    console.error(
      `${LOG_PREFIX}:${chargeSessionId} - Error in charge post processing registered lambda:`,
      err.message,
    );
    throw new Error(`
      {
        status: ${500},
        message: ${err.message}
      }
    `);
  }
};
