const axios = require('axios');

// Ensure axios defaults are properly initialized
if (!axios.defaults) {
  axios.defaults = {};
}
if (!axios.defaults.headers) {
  axios.defaults.headers = {};
}
if (!axios.defaults.headers.common) {
  axios.defaults.headers.common = {};
}
const { print: graphQlPrint } = require('graphql');

const AUTH_URL =
  process.env.REACT_APP_ANONYMOUS_AUTH_REFRESHABLE_TOKEN_URI ||
  'http://localhost:4015/api/initrefreshsession';
const AUTH_API_KEY = process.env.REACT_APP_ANONYMOUS_API_KEY || 'key';

const QA_TOKEN_URL =
  process.env.QA_TOKEN_URL ||
  'https://rhr72lmm0g.execute-api.eu-west-2.amazonaws.com/Chargemaster/fetchtoken';
const QA_TOKEN_API_KEY =
  process.env.QA_TOKEN_API_KEY || '1eZvSKBZR173GozHmkvEh66rNfFPrLqgbHWED1lc';

const SPREEDLY_URL = process.env.SPREEDLY_URL || 'https://core.spreedly.com/v1';
const SPREEDLY_QA_ENV_KEY =
  process.env.SPREEDLY_QA_ENV_KEY || '6NvYjQ5GlVGJkbxotvpVuTh2s8B';

axios.defaults.headers.common['x-api-key'] = AUTH_API_KEY;

const body = {
  payment_method: {
    credit_card: {
      first_name: 'Joe',
      last_name: 'Jones',
      number: '****************',
      verification_value: '423',
      month: '3',
      year: '2029',
      company: 'Acme Inc.',
      address1: '33 Lane Road',
      address2: 'Apartment 4',
      city: 'London',
      state: 'Greater London',
      zip: 'SE33PQ',
      country: 'UK',
      phone_number: '*********',
      shipping_address1: '33 Lane Road',
      shipping_address2: 'Apartment 4',
      shipping_city: 'London',
      shipping_state: 'Greater London',
      shipping_zip: 'SE33PQ',
      shipping_country: 'UK',
      shipping_phone_number: '*********',
    },
    email: '<EMAIL>',
    metadata: {
      key: 'string value',
      another_key: 123,
      final_key: true,
    },
  },
};

const spreedlyTokenBody = {
  payment_method: {
    credit_card: {
      first_name: 'Joe',
      last_name: 'Jones',
      number: '4440 0000 4220 0014',
      verification_value: '100',
      month: '1',
      year: '2039',
      company: 'Acme Inc.',
      address1: '33 Lane Road',
      address2: 'Apartment 4',
      city: 'London',
      state: 'Greater London',
      zip: 'SE33PQ',
      country: 'UK',
      phone_number: '*********',
      shipping_address1: '33 Lane Road',
      shipping_address2: 'Apartment 4',
      shipping_city: 'London',
      shipping_state: 'Greater London',
      shipping_zip: 'SE33PQ',
      shipping_country: 'UK',
      shipping_phone_number: '*********',
    },
    email: '<EMAIL>',
    metadata: {
      key: 'string value',
      another_key: 123,
      final_key: true,
    },
  },
};

let URL;
let API_KEY;

const init = (url, apiKey) => {
  URL = url;
  API_KEY = apiKey;
};

const makeRequestGraphql = ({ schema, variables, extraHeaders, url }) => {
  const body = {
    query: graphQlPrint(schema),
    variables,
  };
  const headers = {
    'x-api-key': API_KEY,
    ...extraHeaders,
    scope: 'integration-test',
  };
  return axios
    .post(url || URL, body, {
      headers,
    })
    .then((response) => {
      return response?.data?.errors?.length > 0
        ? response?.data?.errors[0]
        : response?.data?.data;
    })
    .catch((e) => {
      console.log('Error making graphql request', e);
      return e;
    });
};

const makeGraphqlRequest = async ({ schema, variables, extraHeaders, url }) => {
  const body = {
    query: graphQlPrint(schema),
    variables,
  };

  const headers = {
    'x-api-key': API_KEY,
    ...extraHeaders,
    scope: 'integration-test',
  };

  const response = await axios.post(url || URL, body, { headers });

  if (response?.data?.errors?.length) {
    console.error(`Gql request failed`, {
      headers: response.headers,
      errors: response.data.errors,
    });

    throw response?.data?.errors[0];
  }

  return response?.data?.data;
};

const getToken = () =>
  axios
    .get(AUTH_URL, {
      headers: { authorization: AUTH_API_KEY },
    })
    .then((response) => {
      return response.data.sessionToken;
    })
    .catch((e) => {
      console.log('auth server down!', e);
      throw e;
    });

const getQAToken = (country, userId, feature) =>
  axios
    .get(
      QA_TOKEN_URL +
        `?countryCode=${country}&userId=${userId}&feature=${feature}`,
      {
        headers: { 'x-api-key': QA_TOKEN_API_KEY },
      },
    )
    .then((response) => {
      if (response.data && response.data.token) {
        return response.data.token;
      }
      throw new Error(`invalid token for user id: ${userId}`);
    })
    .catch((e) => {
      console.log('Could not fetch QA token!', e);
      throw e;
    });

const getAnonUser = () =>
  axios
    .post(AUTH_URL, {
      noCookie: true,
    })
    .then((response) => {
      return response.data;
    })
    .catch((e) => {
      console.log('auth server down!', e);
      throw e;
    });

const getSpreedlyToken = async () => {
  const result = await axios
    .post(SPREEDLY_URL + '/payment_methods.json', spreedlyTokenBody, {
      headers: {
        'Content-Type': 'application/json',
      },
      params: { environment_key: SPREEDLY_QA_ENV_KEY },
    })
    .then((response) => {
      return response.data.transaction.payment_method.token;
    })
    .catch((e) => {
      console.log('There was an error fetching a spreedly token', e);
      throw e;
    });
  return result;
};

const retryRequest = async (requestPromise, retries = 3, delay = 1000) => {
  try {
    const resp = await requestPromise();
    if (resp?.code === 500) {
      throw new Error('Server error');
    }
    return resp;
  } catch (e) {
    if (retries > 0) {
      console.error(`Retrying request, ${retries} retries left.`);

      await new Promise((resolve) => setTimeout(resolve, delay));

      return retryRequest(requestPromise, retries - 1, delay);
    }
    throw e;
  }
};

module.exports = {
  retryRequest,
  makeRequestGraphql,
  makeGraphqlRequest,
  init,
  getToken,
  getAnonUser,
  getSpreedlyToken,
  getQAToken,
};
