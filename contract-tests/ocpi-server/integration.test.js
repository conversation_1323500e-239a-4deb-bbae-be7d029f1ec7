const axios = require('axios');

// Ensure axios defaults are properly initialized
if (!axios.defaults) {
  axios.defaults = {};
}
if (!axios.defaults.headers) {
  axios.defaults.headers = {};
}
if (!axios.defaults.headers.common) {
  axios.defaults.headers.common = {};
}

const OCPI_IDENTIFIER = process.env.OCPI_IDENTIFIER || 'ocpi-test-provider-dev';

const OCPI_IDENTIFIER_API_KEY =
  process.env.OCPI_IDENTIFIER_API_KEY ||
  'tQ0tt5mZwnc5MkBr2LhxZqJtBpVHF90Brm0KoFeDjtnukxpcaxw30IkbEa3aCycG';

const OCPI_SERVER_URL =
  process.env.OCPI_SERVER_URL ||
  'https://adb1kgcx00.execute-api.eu-west-2.amazonaws.com/Chargemaster';

const OCPI_GATEWAY_URL =
  `${OCPI_SERVER_URL}/${OCPI_IDENTIFIER}` ||
  `https://adb1kgcx00.execute-api.eu-west-2.amazonaws.com/Chargemaster/${OCPI_IDENTIFIER}`;

const OCPI_BAD_GATEWAY_URL =
  `${OCPI_SERVER_URL}/bad-ocpi-identfier` ||
  'https://adb1kgcx00.execute-api.eu-west-2.amazonaws.com/Chargemaster/bad-ocpi-identfier';

const OCPI_COMMANDS_URL = `${OCPI_GATEWAY_URL}/ocpi/emsp/2.1.1/commands/commandName/tokenId/CommandId`;
const OCPI_CDRS_URL = `${OCPI_GATEWAY_URL}/ocpi/emsp/2.1.1/cdrs`;
const OCPI_SESSIONS_URL = `${OCPI_GATEWAY_URL}/ocpi/cpo/2.1.1/sessions/countryCode/partyId/SessionId`;

axios.defaults.headers.common['Authorization'] = OCPI_IDENTIFIER_API_KEY;

const missingOCPIIdentifierError = {
  error: 'Malformed url',
  status: 400,
};

const invalidUrlGatewayResponse = {
  error: 'Forbidden',
  status: 403,
};

describe('OCPI server', () => {
  describe('Versions', () => {
    it('should return a list of urls', async () => {
      const { data: response } = await axios
        .get(`${OCPI_GATEWAY_URL}/ocpi/emsp/versions`)
        .catch((e) => {
          console.log('axios error', e);
        });

      // Check If data Exists
      expect(response.data).toBeDefined();

      // Check If Version Is 2.1.1 In type
      expect(response.data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            version: '2.1.1',
          }),
        ]),
      );

      // Check If url Contains 2.1.1
      expect(response.data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            url: expect.stringContaining('2.1.1'),
          }),
        ]),
      );

      // Check If url Contains opciIdentifier
      expect(response.data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            url: expect.stringContaining(OCPI_IDENTIFIER),
          }),
        ]),
      );
    });

    it('should throw when OCPI_IDENTIFIER is not present', async () => {
      let thrownError;
      try {
        await axios.get(`${OCPI_SERVER_URL}/ocpi/emsp/versions`);
      } catch (error) {
        thrownError = {
          error: error.response.statusText,
          status: error.response.status,
        };
      }
      expect(thrownError).toEqual(invalidUrlGatewayResponse);
    });
  });

  describe('Version 2.1.1', () => {
    it('should return a list of urls (commands, cdr, credentials) matching 2.1.1', async () => {
      const { data: response } = await axios
        .get(`${OCPI_GATEWAY_URL}/ocpi/emsp/2.1.1`)
        .catch((e) => {
          console.log('axios error', e);
        });

      // Check If data Exists
      expect(response.data).toHaveProperty('endpoints');

      // Check If url Contains 2.1.1
      expect(response.data.endpoints).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            url: expect.stringContaining('2.1.1'),
          }),
        ]),
      );

      // Check If url Contains credentials
      expect(response.data.endpoints).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            identifier: 'credentials',
            url: expect.stringContaining('credentials'),
          }),
        ]),
      );

      // Check If url Contains cdr
      expect(response.data.endpoints).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            identifier: 'cdrs',
            url: expect.stringContaining('cdrs'),
          }),
        ]),
      );

      // Check If url Contains commands
      expect(response.data.endpoints).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            identifier: 'commands',
            url: expect.stringContaining('commands'),
          }),
        ]),
      );

      // Check If url Contains sessions
      expect(response.data.endpoints).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            identifier: 'sessions',
            url: expect.stringContaining('sessions'),
          }),
        ]),
      );
    });

    it('should throw when OCPI_IDENTIFIER is not present', async () => {
      let thrownError;
      try {
        await axios.get(`${OCPI_SERVER_URL}/ocpi/emsp/2.1.1`);
      } catch (error) {
        thrownError = {
          error: error.response.statusText,
          status: error.response.status,
        };
      }
      expect(thrownError).toEqual(invalidUrlGatewayResponse);
    });

    it('should throw when version is not part of the supported versions enum', async () => {
      let thrownError;
      try {
        await axios.get(`${OCPI_GATEWAY_URL}/ocpi/emsp/2.1.2`);
      } catch (error) {
        thrownError = {
          error: error.response.statusText,
          status: error.response.status,
        };
      }
      expect(thrownError).toEqual(invalidUrlGatewayResponse);
    });
  });
});

describe('OCPI Gateway Kinessis stream', () => {
  describe('Commands Stream', () => {
    it('should return correct response', async () => {
      const response = await axios
        .post(OCPI_COMMANDS_URL, {
          body: {
            test: 'test',
          },
        })
        .catch((e) => {
          console.log('axios error', e);
        });

      // Check If data Exists
      expect(response).toBeDefined();

      // Check Fields Are Present On The Response
      expect(response.data.EncryptionType).toBeDefined();
      expect(response.data.SequenceNumber).toBeDefined();
      expect(response.data.ShardId).toBeDefined();
    });

    it('should return correct error response', async () => {
      let thrownError;
      try {
        await axios.post(
          `${OCPI_BAD_GATEWAY_URL}/ocpi/emsp/2.1.1/commands/commandName/tokenId/CommandId`,
          {
            body: {
              test: 'test',
            },
            headers: {
              Authorization: `${OCPI_IDENTIFIER_API_KEY}-invalid-api-key`,
            },
          },
        );
      } catch (error) {
        thrownError = {
          error: error.response.data.Message,
          status: error.response.status,
        };
      }
      expect(thrownError).toEqual({
        error:
          'User is not authorized to access this resource with an explicit deny',
        status: 403,
      });
    });
  });

  describe('CDRS Stream', () => {
    it('should return correct response', async () => {
      const response = await axios
        .post(OCPI_CDRS_URL, {
          body: {
            test: 'test',
          },
        })
        .catch((e) => {
          console.log('axios error', e);
        });

      // Check If data Exists
      expect(response).toBeDefined();

      // Check Fields Are Present On The Response
      expect(response.data.EncryptionType).toBeDefined();
      expect(response.data.SequenceNumber).toBeDefined();
      expect(response.data.ShardId).toBeDefined();
    });

    it('should return correct error response', async () => {
      let thrownError;
      try {
        await axios.post(`${OCPI_BAD_GATEWAY_URL}/ocpi/emsp/2.1.1/cdrs`, {
          body: {
            test: 'test',
          },
          headers: {
            Authorization: `${OCPI_IDENTIFIER_API_KEY}-invalid-api-key`,
          },
        });
      } catch (error) {
        thrownError = {
          error: error.response.data.Message,
          status: error.response.status,
        };
      }
      expect(thrownError).toEqual({
        error:
          'User is not authorized to access this resource with an explicit deny',
        status: 403,
      });
    });
  });

  describe('Sessions Stream', () => {
    it('should return correct response', async () => {
      const response = await axios
        .put(OCPI_SESSIONS_URL, {
          body: {
            test: 'test',
          },
        })
        .catch((e) => {
          console.log('axios error', e);
        });

      // Check If data Exists
      expect(response).toBeDefined();

      // Check Fields Are Present On The Response
      expect(response.data.EncryptionType).toBeDefined();
      expect(response.data.SequenceNumber).toBeDefined();
      expect(response.data.ShardId).toBeDefined();
    });

    it('should return correct error response', async () => {
      let thrownError;
      try {
        await axios.put(
          `${OCPI_BAD_GATEWAY_URL}/ocpi/cpo/2.1.1/sessions/countryCode/partyId/SessionId`,
          {
            body: {
              test: 'test',
            },
            headers: {
              Authorization: `${OCPI_IDENTIFIER_API_KEY}-invalid-api-key`,
            },
          },
        );
      } catch (error) {
        thrownError = {
          error: error.response.data.Message,
          status: error.response.status,
        };
      }
      expect(thrownError).toEqual({
        error:
          'User is not authorized to access this resource with an explicit deny',
        status: 403,
      });
    });
  });
});
