# mobile pipeline: using React Native
pool:
  name: GenericPoolLinux-SS
trigger: none
parameters:
  - name: environment
    displayName: Pick environment
    type: string
    default: test
    values:
      - test
      - preprod
      - performance
      - prod
      - hotfix
      - us-uat
      - us-prod
  - name: snow_cr
    displayName: SNOW Change Request ID
    type: string
    default: 'Insert Change Request ID when running Prod pipeline'
  - name: snow_create_prod
    displayName: Create Snow CR
    type: boolean
    default: false
  - name: prodChangeCrDescription
    type: string
    default: 'Add reason for prod deployment if using automated CR'
    displayName: Prod Change Description
  - name: hotifx_commit
    displayName: Hotfix Commit ID
    type: string
    default: 'Insert Hotfix Commit ID when the pipeline as hotfix'
  - name: anonymous_user
    displayName: Include Anonymous User Server
    type: boolean
    default: false
  - name: anonymous_user_server_tag
    displayName: Deploy Anonymous User Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2024-11-06_12-55-16.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: anonymous_user_version_number
    displayName: Anonymous User Server Version Number(if Custom)
    type: string
    default: 'Check tag list for anonymous user server(eg: 2024-02-23_05-26-12)'
  - name: charge
    displayName: Include Charge Server
    type: boolean
    default: false
  - name: charge_server_tag
    displayName: Deploy Charge Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_15-14-32.dev
      - 2025-07-17_09-08-25.test
      - 2025-07-17_09-08-25.preprod
      - 2025-07-03_16-48-59.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: charge_version_number
    displayName: Charge Server Version Number(if Custom)
    type: string
    default: 'Check tag list for charge server(eg: 2024-02-23_05-26-12)'
  - name: favourites
    displayName: Include Favourites Server
    type: boolean
    default: false
  - name: favourites_server_tag
    displayName: Deploy Favourites Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2024-05-30_13-32-49.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: favourites_version_number
    displayName: Favourites Server Version Number(if Custom)
    type: string
    default: 'Check tag list for favourites server(eg: 2024-02-23_05-26-12)'
  - name: history
    displayName: Include History Server
    type: boolean
    default: false
  - name: history_server_tag
    displayName: Deploy History Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-28_16-15-37.test
      - 2025-05-28_16-15-37.preprod
      - 2025-05-28_16-15-37.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: history_version_number
    displayName: History Server Version Number(if Custom)
    type: string
    default: 'Check tag list for history server(eg: 2024-02-23_05-26-12)'
  - name: invoices
    displayName: Include Invoices Server
    type: boolean
    default: false
  - name: invoices_server_tag
    displayName: Deploy Invoices Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2024-05-30_13-26-18.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: invoices_version_number
    displayName: Invoices Server Version Number(if Custom)
    type: string
    default: 'Check tag list for invoices server(eg: 2024-02-23_05-26-12)'
  - name: map
    displayName: Include Map Server
    type: boolean
    default: false
  - name: map_server_tag
    displayName: Deploy Map Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_17-36-12.dev
      - 2025-07-23_13-32-44.test
      - 2025-05-09_08-04-24.preprod
      - 2025-05-09_08-04-24.prod
      - 2025-05-13_02-13-15.performance
      - Hotfix
      - Custom
  - name: map_version_number
    displayName: Map Server Version Number(if Custom)
    type: string
    default: 'Check tag list for map server(eg: 2024-02-23_05-26-12)'
  - name: payments_bppay
    displayName: Include Payments BPpay Server
    type: boolean
    default: false
  - name: payments_bppay_server_tag
    displayName: Deploy Payments Bppay Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_15-14-32.dev
      - 2025-07-21_09-30-43.test
      - 2025-07-17_09-08-25.preprod
      - 2025-07-07_14-55-21.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: payments_bppay_version_number
    displayName: Payments BPpay Server Version Number(if Custom)
    type: string
    default: 'Check tag list for payments bppay server(eg: 2024-02-23_05-26-12)'
  - name: payments_gocardless
    displayName: Include Payments gocardless Server
    type: boolean
    default: false
  - name: payments_gocardless_server_tag
    displayName: Deploy Payments Gocardless Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-07-16_12-21-43.test
      - 2025-07-16_12-21-43.preprod
      - test3.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: payments_gocardless_version_number
    displayName: Payments gocardless Server Version Number(if Custom)
    type: string
    default: 'Check tag list for payments gocardless server(eg: 2024-02-23_05-26-12)'
  - name: payments_stripe
    displayName: Include Payments Stripe Server
    type: boolean
    default: false
  - name: payments_stripe_server_tag
    displayName: Deploy Payments Stripe Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2024-05-30_13-20-38.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: payments_stripe_version_number
    displayName: Payments Stripe Server Version Number(if Custom)
    type: string
    default: 'Check tag list for payments stripe server(eg: 2024-02-23_05-26-12)'
  - name: pdf
    displayName: Include pdf Server
    type: boolean
    default: false
  - name: pdf_server_tag
    displayName: Deploy Pdf Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2025-02-26_08-58-34.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: pdf_version_number
    displayName: pdf Server Version Number(if Custom)
    type: string
    default: 'Check tag list for pdf server(eg: 2024-02-23_05-26-12)'
  - name: user
    displayName: Include User Server
    type: boolean
    default: false
  - name: user_server_tag
    displayName: Deploy User Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-07-10_16-58-00.test
      - 2025-07-10_16-58-00.preprod
      - 2025-06-30_18-56-04.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: user_version_number
    displayName: User Server Version Number(if Custom)
    type: string
    default: 'Check tag list for user server(eg: 2024-02-23_05-26-12)'
  - name: rfid
    displayName: Include RFID Server
    type: boolean
    default: false
  - name: rfid_server_tag
    displayName: Deploy RFID Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_14-06-09.dev
      - 2025-07-18_10-22-50.test
      - 2025-07-18_10-22-50.preprod
      - 2025-05-27_11-11-07.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: rfid_version_number
    displayName: RFID Server Version Number(if Custom)
    type: string
    default: 'Check tag list for RFID server(eg: 2024-02-23_05-26-12)'
  - name: gateway_public
    displayName: Include Gateway Public Server
    type: boolean
    default: false
  - name: gateway_public_server_tag
    displayName: Deploy Gateway Public Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2024-08-20_06-23-46.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: gateway_public_version_number
    displayName: Gateway Public Server Version Number(if Custom)
    type: string
    default: 'Check tag list for gateway public server(eg: 2024-02-23_05-26-12)'
  - name: gateway_private
    displayName: Include Gateway Private Server
    type: boolean
    default: false
  - name: gateway_private_server_tag
    displayName: Deploy Gateway Private Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2024-10-04_12-06-10.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: gateway_private_version_number
    displayName: Gateway Private Server Version Number(if Custom)
    type: string
    default: 'Check tag list for gateway private server(eg: 2024-02-23_05-26-12)'
  - name: voucher
    displayName: Include Voucher Server
    type: boolean
    default: false
  - name: voucher_server_tag
    displayName: Deploy Voucher Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2025-06-05_12-11-47.hotfix.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: voucher_version_number
    displayName: Voucher Server Version Number(if Custom)
    type: string
    default: 'Check tag list for voucher server(eg: 2024-02-23_05-26-12)'
  - name: wallet
    displayName: Include Wallet Server
    type: boolean
    default: false
  - name: wallet_server_tag
    displayName: Deploy Wallet Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-07-16_12-21-43.test
      - 2025-07-16_12-21-43.preprod
      - 2025-01-28_08-10-56.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: wallet_version_number
    displayName: Wallet Server Version Number(if Custom)
    type: string
    default: 'Check tag list for wallet server(eg: 2024-02-23_05-26-12)'
  - name: prices
    displayName: Include Prices Server
    type: boolean
    default: false
  - name: prices_server_tag
    displayName: Deploy Prices Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-07-23_09-45-23.test
      - 2025-05-09_08-04-24.preprod
      - 2025-04-17_16-06-23.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: prices_version_number
    displayName: Prices Server Version Number(if Custom)
    type: string
    default: 'Check tag list for prices server(eg: 2024-02-23_05-26-12)'
  - name: test_provider
    displayName: Include Test Provider Server(except PROD env)
    type: boolean
    default: false
  - name: test_provider_server_tag
    displayName: Deploy Test Provider Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: test_provider_version_number
    displayName: Test Provider Server Version Number(if Custom)
    type: string
    default: 'Check tag list for test provider server(eg: 2024-02-23_05-26-12)'
  - name: ocpi_server
    displayName: Include Ocpi Server
    type: boolean
    default: false
  - name: ocpi_server_tag
    displayName: Deploy Ocpi Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2025-04-25_10-06-07.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: ocpi_server_version_number
    displayName: Ocpi Credentials Server Version Number(if Custom)
    type: string
    default: 'Check tag list for ocpi server(eg: 2024-02-23_05-26-12)'
  - name: offer_server
    displayName: Include Offer Server
    type: boolean
    default: false
  - name: offer_server_tag
    displayName: Deploy Offer Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_09-45-23.dev
      - 2025-07-10_16-58-00.test
      - 2025-05-09_08-04-24.preprod
      - 2024-11-29_15-57-15.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: offer_server_version_number
    displayName: Offer Server Version Number(if Custom)
    type: string
    default: 'Check tag list for offer server(eg: 2024-02-23_05-26-12)'
  - name: subscription_server
    displayName: Include Subscription Server
    type: boolean
    default: false
  - name: subscription_server_tag
    displayName: Deploy Subscription Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-23_15-14-32.dev
      - 2025-05-09_08-04-24.test
      - 2025-05-09_08-04-24.preprod
      - 2025-04-17_14-06-03.prod
      - 2025-05-09_08-04-24.performance
      - Hotfix
      - Custom
  - name: subscription_server_version_number
    displayName: Subscription Server Version Number(if Custom)
    type: string
    default: 'Check tag list for subscription server(eg: 2024-02-23_05-26-12)'
resources:
  repositories:
    # Wiz.io
    - repository: coss-templates
      type: git
      name: Vulnerability_Identification_and_Awareness/coss-templates
      ref: master
stages:
  - template: ../stages/microservice_unified.yaml
    parameters:
      environment: ${{ parameters.environment }}
      snowCR: ${{ parameters.snow_cr }}
      prodChangeCrDescription: ${{ parameters.prodChangeCrDescription }}
      snow_create_prod: ${{ parameters.snow_create_prod }}
      hotifx_commit: ${{ parameters.hotifx_commit }}
      workItems: Microservice Deployment
      ${{ if in(parameters.environment, 'us-uat', 'preprod', 'performance', 'prod', 'us-prod', 'hotfix') }}:
        apiVersion: 7
      ${{ if eq(parameters.environment, 'test') }}:
        apiVersion: x
      gatewayFeatures:
        - name: gateway-public-server
          deployName: gateway-public-server
          hpapdbName: chargemaster-gateway-public
          stageName: gateway_public_server
          bool: ${{ parameters.gateway_public }}
          namespace: gateway-server
          deploymentName: gateway-public-server-deployment
          microserviceVersion: ${{ parameters.gateway_public_version_number }}
          deployTag: ${{ parameters.gateway_public_server_tag }}
          targetPort: 4031
        - name: gateway-private-server
          deployName: gateway-private-server
          hpapdbName: chargemaster-gateway
          stageName: gateway_private_server
          bool: ${{ parameters.gateway_private }}
          namespace: gateway-server
          deploymentName: gateway-private-server-deployment
          microserviceVersion: ${{ parameters.gateway_private_version_number }}
          deployTag: ${{ parameters.gateway_private_server_tag }}
          targetPort: 4030
      features:
        - name: anonymous-user-server
          deployName: anonymous-server
          hpapdbName: anonymous-server
          stageName: anonymous_user_server
          bool: ${{ parameters.anonymous_user }}
          namespace: anonymous-server
          deploymentName: anonymous-server-deployment
          microserviceVersion: ${{ parameters.anonymous_user_version_number }}
          deployTag: ${{ parameters.anonymous_user_server_tag }}
          targetPort: 4015
        - name: charge-server
          deployName: charge-server
          hpapdbName: charge-server
          stageName: charge_server
          bool: ${{ parameters.charge }}
          namespace: charge-server
          deploymentName: charge-server-deployment
          microserviceVersion: ${{ parameters.charge_version_number }}
          deployTag: ${{ parameters.charge_server_tag }}
          targetPort: 4004
        - name: favourites-server
          deployName: favourites-server
          hpapdbName: favourites-server
          stageName: favourites_server
          bool: ${{ parameters.favourites }}
          namespace: favourites-server
          deploymentName: favourites-server-deployment
          microserviceVersion: ${{ parameters.favourites_version_number }}
          deployTag: ${{ parameters.favourites_server_tag }}
          targetPort: 4009
        - name: history-server
          deployName: history-server
          hpapdbName: history-server
          stageName: history_server
          bool: ${{ parameters.history }}
          namespace: history-server
          deploymentName: history-server-deployment
          microserviceVersion: ${{ parameters.history_version_number }}
          deployTag: ${{ parameters.history_server_tag }}
          targetPort: 4007
        - name: invoices-server
          deployName: invoices-server
          hpapdbName: invoices-server
          stageName: invoices_server
          bool: ${{ parameters.invoices }}
          namespace: invoices-server
          deploymentName: invoices-server-deployment
          microserviceVersion: ${{ parameters.invoices_version_number }}
          deployTag: ${{ parameters.invoices_server_tag }}
          targetPort: 4008
        - name: map-server
          deployName: map-server
          hpapdbName: map-server
          stageName: map_server
          bool: ${{ parameters.map }}
          namespace: map-server
          deploymentName: map-server-deployment
          microserviceVersion: ${{ parameters.map_version_number }}
          deployTag: ${{ parameters.map_server_tag }}
          targetPort: 4003
        - name: payments-bppay-server
          deployName: bppay-server
          hpapdbName: bppay-server
          stageName: payments_bppay_server
          bool: ${{ parameters.payments_bppay }}
          namespace: payments-server
          deploymentName: bppay-server-deployment
          microserviceVersion: ${{ parameters.payments_bppay_version_number }}
          deployTag: ${{ parameters.payments_bppay_server_tag }}
          targetPort: 4011
        - name: payments-gocardless-server
          deployName: gocardless-server
          hpapdbName: gocardless-server
          stageName: payments_gocardless_server
          bool: ${{ parameters.payments_gocardless }}
          namespace: gocardless-server
          deploymentName: gocardless-server-deployment
          microserviceVersion: ${{ parameters.payments_gocardless_version_number }}
          deployTag: ${{ parameters.payments_gocardless_server_tag }}
          targetPort: 4010
        - name: payments-stripe-server
          deployName: payments-server
          hpapdbName: payments-server
          stageName: payments_stripe_server
          bool: ${{ parameters.payments_stripe }}
          namespace: payments-server
          deploymentName: payments-server-deployment
          microserviceVersion: ${{ parameters.payments_stripe_version_number }}
          deployTag: ${{ parameters.payments_stripe_server_tag }}
          targetPort: 4005
        - name: pdf-server
          deployName: pdf-server
          hpapdbName: pdf-server
          stageName: pdf_server
          bool: ${{ parameters.pdf }}
          namespace: pdf-server
          deploymentName: pdf-server-deployment
          microserviceVersion: ${{ parameters.pdf_version_number }}
          deployTag: ${{ parameters.pdf_server_tag }}
          targetPort: 4016
        - name: user-server
          deployName: user-server
          hpapdbName: user-server
          stageName: user_server
          bool: ${{ parameters.user }}
          namespace: user-server
          deploymentName: user-server-deployment
          microserviceVersion: ${{ parameters.user_version_number }}
          deployTag: ${{ parameters.user_server_tag }}
          targetPort: 4002
        - name: rfid-server
          deployName: rfid-server
          hpapdbName: rfid-server
          stageName: rfid_server
          bool: ${{ parameters.rfid }}
          namespace: rfid-server
          deploymentName: rfid-server-deployment
          microserviceVersion: ${{ parameters.rfid_version_number }}
          deployTag: ${{ parameters.rfid_server_tag }}
          targetPort: 4017
        - name: voucher-server
          deployName: voucher-server
          hpapdbName: voucher-server
          stageName: voucher_server
          bool: ${{ parameters.voucher }}
          namespace: voucher-server
          deploymentName: voucher-server-deployment
          microserviceVersion: ${{ parameters.voucher_version_number }}
          deployTag: ${{ parameters.voucher_server_tag }}
          targetPort: 4012
        - name: wallet-server
          deployName: wallet-server
          hpapdbName: wallet-server
          stageName: wallet_server
          bool: ${{ parameters.wallet }}
          namespace: wallet-server
          deploymentName: wallet-server-deployment
          microserviceVersion: ${{ parameters.wallet_version_number }}
          deployTag: ${{ parameters.wallet_server_tag }}
          targetPort: 4014
        - name: prices-server
          deployName: prices-server
          hpapdbName: prices-server
          stageName: prices_server
          bool: ${{ parameters.prices }}
          namespace: prices-server
          deploymentName: prices-server-deployment
          microserviceVersion: ${{ parameters.prices_version_number }}
          deployTag: ${{ parameters.prices_server_tag }}
          targetPort: 4013
        - name: test-provider-server
          deployName: test-provider
          hpapdbName: test-provider
          stageName: test_provider_server
          bool: ${{ parameters.test_provider }}
          namespace: provider-server
          deploymentName: test-provider-deployment
          microserviceVersion: ${{ parameters.test_provider_version_number }}
          deployTag: ${{ parameters.test_provider_server_tag }}
          targetPort: 4041
        - name: ocpi-server
          deployName: ocpi-server
          hpapdbName: ocpi-server
          stageName: ocpi_server
          bool: ${{ parameters.ocpi_server }}
          namespace: ocpi-server
          deploymentName: ocpi-server-deployment
          microserviceVersion: ${{ parameters.ocpi_server_version_number }}
          deployTag: ${{ parameters.ocpi_server_tag }}
          targetPort: 4019
        - name: offer-server
          deployName: offer-server
          hpapdbName: offer-server
          stageName: offer_server
          bool: ${{ parameters.offer_server }}
          namespace: offer-server
          deploymentName: offer-server-deployment
          microserviceVersion: ${{ parameters.offer_server_version_number }}
          deployTag: ${{ parameters.offer_server_tag }}
          targetPort: 4020
        - name: subscription-server
          deployName: subscription-server
          hpapdbName: subscription-server
          stageName: subscription_server
          bool: ${{ parameters.subscription_server }}
          namespace: subscription-server
          deploymentName: subscription-server-deployment
          microserviceVersion: ${{ parameters.subscription_server_version_number }}
          deployTag: ${{ parameters.subscription_server_tag }}
          targetPort: 4021
        - name: gateway-public-server
          deployName: gateway-public-server
          hpapdbName: chargemaster-gateway-public
          stageName: gateway_public_server
          bool: ${{ parameters.gateway_public }}
          namespace: gateway-server
          deploymentName: gateway-public-server-deployment
          microserviceVersion: ${{ parameters.gateway_public_version_number }}
          deployTag: ${{ parameters.gateway_public_server_tag }}
          targetPort: 4031
        - name: gateway-private-server
          deployName: gateway-private-server
          hpapdbName: chargemaster-gateway
          stageName: gateway_private_server
          bool: ${{ parameters.gateway_private }}
          namespace: gateway-server
          deploymentName: gateway-private-server-deployment
          microserviceVersion: ${{ parameters.gateway_private_version_number }}
          deployTag: ${{ parameters.gateway_private_server_tag }}
          targetPort: 4030
