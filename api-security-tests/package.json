{"name": "api-security-tests", "version": "1.0.0", "main": "build/index.js", "scripts": {"format": "prettier --ignore-path .gitignore --write \"**/*.+(ts|json)\"", "lint": "eslint ./ --ext .ts", "test": "jest --maxWorkers=100%", "test:ci": "jest --maxWorkers=100% --ci"}, "dependencies": {"axios": "^1.10.0", "date-fns": "^2.24.0", "dotenv": "^16.3.1", "graphql": "^15.3.0", "graphql-tag": "^2.11.0", "jsonwebtoken": "^9.0.1", "jwks-rsa": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@types/jest": "^29.5.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-jest": "^29.5.0", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "jest": "^29.5.0", "prettier": "^3.0.0", "ts-jest": "^29.1.1", "typescript": "^5.1.6"}}