import axios from 'axios';

// Ensure axios defaults are properly initialized
if (!axios.defaults) {
  axios.defaults = {};
}
if (!axios.defaults.headers) {
  axios.defaults.headers = {};
}
if (!axios.defaults.headers.common) {
  axios.defaults.headers.common = {};
}
import { ASTNode, print } from 'graphql';
const AUTH_URL = process.env.ANONYMOUS_AUTH_REFRESHABLE_TOKEN_URI || '';
const AUTH_API_KEY = process.env.ANONYMOUS_API_KEY || '';

const QA_TOKEN_URL = process.env.QA_TOKEN_URL || '';
const QA_TOKEN_API_KEY = process.env.QA_TOKEN_API_KEY || '';
const APOLLO_INTERNAL_SECRET = process.env.APOLLO_INTERNAL_SECRET || '';

const API_KEY = process.env.API_GATEWAY_KEY || '';
const PRIVATE_SERVER = process.env.PRIVATE_GATEWAY_SERVER_HTTP_TESTS || '';
const PUBLIC_SERVER = process.env.PUBLIC_GATEWAY_SERVER_HTTP || '';

axios.defaults.headers.common['x-api-key'] = AUTH_API_KEY;

type PrivateRequest = {
  schema: ASTNode;
  variables: unknown;
  sessionToken: string;
};

export const makeGraphqlRequest = ({
  schema,
  variables,
  sessionToken,
}: PrivateRequest) => {
  const body = {
    query: print(schema),
    variables,
  };

  return axios
    .post(PRIVATE_SERVER, body, {
      headers: {
        'x-api-key': API_KEY,
        Authorization: sessionToken,
        'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
        scope: 'integration-test',
      },
      timeout: 20000,
    })
    .then((response) => {
      if (response?.data?.errors?.length) {
        return response?.data.errors[0];
      }

      return {
        data: response?.data?.data,
        status: response.status,
      };
    })
    .catch((e) => {
      return errorBody(e);
    });
};

type PublicRequest = {
  schema: ASTNode;
  variables: unknown;
};

export const makePublicGraphqlRequest = ({
  schema,
  variables,
}: PublicRequest) => {
  const body = {
    query: print(schema),
    variables,
  };

  const headers = {
    'x-api-key': API_KEY,
  };

  return axios
    .post(PUBLIC_SERVER, body, {
      headers,
      timeout: 20000,
    })
    .then((response) => {
      return response?.data?.data;
    })
    .catch((e) => {
      return errorBody(e);
    });
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const errorBody = (e: any) => {
  if (e?.response?.data?.errors?.length) {
    return e?.response?.data.errors[0];
  }

  return {
    message: e?.message,
    data: e?.response?.data,
  };
};

export const getToken = () =>
  axios
    .get(AUTH_URL, {
      headers: { Authorization: AUTH_API_KEY },
    })
    .then((response) => {
      return response.data.sessionToken;
    })
    .catch((e) => {
      console.log('auth server down!', e);
      throw e;
    });

export const getQAToken = (userId: string) =>
  axios
    .get(`${QA_TOKEN_URL}?countryCode=NL&userId=${userId}&feature=charge`, {
      headers: { 'x-api-key': QA_TOKEN_API_KEY },
    })
    .then((response) => {
      return response.data.token;
    })
    .catch((e) => {
      console.log('Could not fetch QA token!', e);
      throw e;
    });

export const getAnonUser = async () =>
  await axios
    .post(AUTH_URL, {
      noCookie: true,
    })
    .then((response) => {
      return response.data;
    })
    .catch((e) => {
      console.log('auth server down!', e);
      throw e;
    });
