const path = require('path');

// all tests which are date dependent will use the GMT time zone
process.env.TZ = 'GMT';

module.exports = {
  projects: [
    path.resolve(__dirname, 'packages', 'ocpi-server'),
    {
      displayName: 'gateway-public-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'gateway-public-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'gateway-private-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'gateway-private-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'map-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'map-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['/node_modules/(?!axios).+\\.js$'],
    },
    {
      displayName: 'offer-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'offer-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'payments-stripe-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'payments-stripe-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'anonymous-user-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'anonymous-user-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'pdf-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'pdf-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      moduleDirectories: ['node_modules', 'src'],
      roots: ['<rootDir>/src'],
      moduleNameMapper: {
        '@utils/(.*)': '<rootDir>/src/utils/$1',
        '@database/(.*)': '<rootDir>/src/database/$1',
        '@services/(.*)': '<rootDir>/src/services/$1',
        '@graphql/(.*)': '<rootDir>/src/graphql/$1',
      },
    },
    {
      displayName: 'payments-bppay-server',
      rootDir: path.resolve(__dirname, 'packages', 'payments-bppay-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['/node_modules/(?!(axios)/)'],
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
    },
    {
      displayName: 'user-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'user-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['node_modules/(?!@bp/pulse-shared-types)/'],
    },
    {
      displayName: 'charge-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'charge-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['/node_modules/(?!axios).+\\.js$'],
    },
    {
      displayName: 'favourites-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'favourites-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'invoices-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'invoices-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'history-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'history-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'payments-gocardless-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(
        __dirname,
        'packages',
        'payments-gocardless-server',
      ),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['/node_modules/(?!(axios)/)'],
    },
    {
      displayName: 'rfid-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'rfid-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['/node_modules/(?!(axios)/)'],
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
    },
    {
      displayName: 'voucher-server',
      rootDir: path.resolve(__dirname, 'packages', 'voucher-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
    },
    {
      displayName: 'prices-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'prices-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
    },
    {
      displayName: 'subscription-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'subscription-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['node_modules/(?!axios)/'],
    },
    {
      displayName: 'wallet-server',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'packages', 'wallet-server'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['/node_modules/(?!(axios)/)'],
    },
    {
      displayName: 'Infrastructure',
      testMatch: ['<rootDir>/**/*.test.(jsx|js|ts|tsx)'],
      rootDir: path.resolve(__dirname, 'infrastructure'),
      testPathIgnorePatterns: ['/node_modules/', 'integration'],
      transformIgnorePatterns: ['node_modules/(?!axios)/'],
    },
  ],
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: 'test-results/jest' }],
  ],
  coverageReporters: [
    'json',
    'lcov',
    'text',
    'clover',
    'text-summary',
    'cobertura',
  ],
  testResultsProcessor: 'jest-sonar-reporter',
};
