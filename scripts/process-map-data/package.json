{"name": "process-map-data", "private": true, "scripts": {"dev": "npm run types:generate -- --watch", "start": "ts-node src/run.ts", "types:check": "tsc --noEmit", "types:generate": "graphql-codegen --config codegen.yml -r dotenv/config -e dotenv_config_path=./.env"}, "dependencies": {"axios": "^1.10.0", "dotenv": "^16.0.2", "graphql": "^16.7.1", "graphql-request": "^6.1.0", "lodash": "^4.17.21"}, "devDependencies": {"@graphql-codegen/cli": "4.0.1", "@graphql-codegen/client-preset": "^4.0.1", "@types/lodash": "^4.14.195", "@types/node": "^17.0.23", "ts-node": "^10.9.1", "typescript": "^4.6.3"}}