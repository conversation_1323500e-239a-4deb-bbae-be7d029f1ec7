{"name": "elastic-search-setup", "private": true, "scripts": {"aws": "ts-node src/run-aws.ts", "postinstall": "rm -rf ./node_modules/hpagent/test", "lab": "ts-node src/run-lab.ts", "local": "ts-node src/run-local.ts", "qa": "ts-node src/run-qa.ts", "setup:aws": "npm i && npm run aws", "setup:lab": "npm i && npm run lab", "setup:local": "npm i && npm run local", "setup:qa": "npm i && npm run qa", "types:check": "tsc --noEmit"}, "dependencies": {"@elastic/elasticsearch": "7.13.0", "axios": "^1.10.0", "dotenv": "^16.0.2"}, "devDependencies": {"@types/node": "^17.0.23", "ts-node": "^10.9.1", "typescript": "^4.6.3"}}