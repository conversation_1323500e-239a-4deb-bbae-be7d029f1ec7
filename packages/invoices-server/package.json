{"name": "@bp/feature-invoices-server", "version": "0.0.1", "private": true, "scripts": {"prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf dist/", "dev": "nodemon --config nodemon.json src/index.ts", "lint": "eslint . --ext .ts", "lint:fix": "npm run lint -- --fix", "start": "node ./dist/index.js", "test": "jest", "test:ci": "jest --ci", "types:check": "tsc --noEmit"}, "dependencies": {"@apollo/federation": "^0.38.1", "apollo-opentracing": "^2.1.35", "apollo-server": "^3.11.1", "apollo-server-core": "^3.4.0", "apollo-server-express": "^3.4.0", "axios": "^0.23.0", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.25.0", "dotenv": "^10.0.0", "express": "^4.17.1", "graphql": "^15.6.1", "helmet": "^4.6.0", "jaeger-client": "^3.18.1", "opentracing": "^0.14.5", "schemaglue": "^4.3.0", "winston": "^3.3.3"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/runtime": "^7.15.4", "@bp/eslint-plugin": "^0.0.3", "@types/compression": "^1.7.2", "@types/helmet": "^4.0.0", "@types/jaeger-client": "^3.18.3", "@types/jest": "^27.0.2", "babel-jest": "^27.3.1", "eslint": "^8.20.0", "jest": "^26.6.3", "nodemon": "^2.0.14", "prettier": "2.7.1", "ts-node": "^10.3.1", "typescript": "^4.4.4"}}