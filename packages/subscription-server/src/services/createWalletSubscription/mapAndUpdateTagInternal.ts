import { CountryCode } from '@bp/pulse-common';
import { Logger } from 'winston';

import { updateTagSchemeInternal } from '../../services/user/updateTagSchemeInternal';
import { TagType } from '../../types/enums';
import { UserInfoResponse } from '../../types/services';

export const mapAndUpdateTagScheme = async (
  userInfo: UserInfoResponse,
  userId: string,
  schemeName: string,
  logger: Logger,
) => {
  const tagUpdatesByCountry = {
    [CountryCode.DE]: [TagType.VIRTUAL_HTB, TagType.PHYSICAL_RFID],
    [CountryCode.UK]: [TagType.VIRTUAL_HTB, TagType.PHYSICAL_RFID],
    [CountryCode.US]: [],
    [CountryCode.NL]: [],
    [CountryCode.ES]: [],
    [CountryCode.FR]: [],
  };
  const tagsToUpdate: TagType[] =
    tagUpdatesByCountry[userInfo.country as CountryCode];

  const tagCardNumbers = userInfo.tagIds
    .filter(
      (tag) =>
        tagsToUpdate.includes(tag.tagNotes as TagType) &&
        tag.tagStatus.toUpperCase() === 'ACTIVE',
    )
    .map((tag) => tag.tagCardNumber);

  if (tagCardNumbers.length > 0) {
    logger.info('Updating tag card numbers: ', tagCardNumbers);
    await updateTagSchemeInternal({
      userId,
      schemeName,
      tagCardNumbers,
    });
  }
};
