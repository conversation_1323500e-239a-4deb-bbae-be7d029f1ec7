import { ChargePayloadMessage, ChargeType } from '../../common/enums';
import { NewChargeEvent, TagItem } from '../../common/interfaces';
import {
  generateChargePayload,
  getChargePayloadData,
  getSiteId,
  getTokenOCPI,
} from './chargevision.functions';

Date.now = jest.fn().mockReturnValue(new Date('2025-07-29T16:33:37.000Z'));

describe('chargevision functions', () => {
  describe('getChargePayloadData', () => {
    it('should return undefined for sessionId when no providerChargeIds are provided', () => {
      const result = getChargePayloadData();
      expect(result).toEqual({ sessionId: undefined });
    });

    it('should return sessionId is set correctly', () => {
      const providerChargeIds = [{ type: 'sessionId', value: '123' }];

      const result = getChargePayloadData(providerChargeIds);
      expect(result).toEqual({ sessionId: '123' });
    });

    it('should return undefined for missing sessionId', () => {
      const providerChargeIds = [{ type: 'tagId', value: 'abc' }];

      const result = getChargePayloadData(providerChargeIds);
      expect(result).toEqual({ sessionId: undefined });
    });
  });

  describe('getSiteId', () => {
    it('should return default value if no siteId', () => {
      const result = getSiteId();
      expect(result).toEqual(0);
    });

    it('should return default value if siteId is not number', () => {
      const result = getSiteId('BPCM');
      expect(result).toEqual(0);
    });

    it('should return siteId', () => {
      const result = getSiteId('BPCM-1234');
      expect(result).toEqual(1234);
    });

    it('should return siteId if only one value is provided', () => {
      const result = getSiteId('1234');
      expect(result).toEqual(1234);
    });
  });

  describe('generateChargePayload', () => {
    const event: NewChargeEvent = {
      userId: 'testUserId1',
      apolloInternalId: 'apolloInternalId1',
      connectorInternalId: 'connectorInternalId1',
      tagId: 'tagId1',
      chargeType: ChargeType.GUEST,
    };

    const message = ChargePayloadMessage.START_CHARGE_CONFIRMED;
    const paymentId = 'paymentId1';

    it('should return correct chargepayload data', () => {
      const result = generateChargePayload(event, paymentId, message);
      expect(result).toEqual({
        chargePayload: {
          apolloInternalId: 'apolloInternalId1',
          connectorInternalId: 'connectorInternalId1',
          paymentId,
          status: 'Success',
          message,
        },
      });
    });

    it('should return chargepayload data without paymentId', () => {
      const result = generateChargePayload(event, undefined, message);
      expect(result).toEqual({
        chargePayload: {
          apolloInternalId: 'apolloInternalId1',
          connectorInternalId: 'connectorInternalId1',
          status: 'Success',
          message,
        },
      });
    });
  });

  describe('getTokenOCPI', () => {
    it('should return OCPI token for registered users', () => {
      const tag: TagItem = {
        tagId: 'tagId1',
        tagCardNumber: 'tagCardNumber1',
        tagLastUsedDateTime: '2021-01-01',
        tagTypeName: '',
        tagCategoryName: '',
        tagNotes: '',
      };
      const result = getTokenOCPI(tag);
      expect(result).toEqual({
        uid: 'tagCardNumber1',
        authId: 'tagCardNumber1',
        lastUpdated: '2021-01-01',
      });
    });

    it('should return OCPI token for guests', () => {
      const result = getTokenOCPI(undefined);
      expect(result).toEqual({
        type: 'AD_HOC_USER',
      });
    });
  });
});
