import { AxiosResponse } from 'axios';

import {
  AuthenticationIndicator,
  AuthenticationIndicatorType,
  ChallengeIndicator,
  CurrencyCode,
  MessageCategory,
  OperationType,
  PaymentService,
  PaymentServiceAPI,
  PaymentType,
  ThreeDSUrl,
} from '../../enums';
import AppTenantConfig from '../../services/bpPay/appTenantConfig';
import logger from '../../utils/logger';
import { callToDPaaS, getPaymentMethod } from './cardinal-helper';
import { logDPaaSError } from './errorLogger';
import {
  AuthLookupParams,
  AuthorisationLookupData,
  AuthParams,
  ChallengeParameters,
  GetTokenParams,
} from './interfaces';

export const getToken = async (
  args: GetTokenParams,
  logTraceId: string,
  correlationId: string,
): Promise<string> => {
  const requestPayload = {
    amount: args.amount,
    currencyCode: CurrencyCode[args.country],
    orderIdentifier: args.orderId,
  };

  try {
    const { data, status: serviceApiResponseCode } = (await callToDPaaS(
      args,
      requestPayload,
      ThreeDSUrl.TOKEN,
      correlationId,
    )) as AxiosResponse<{ token: string; [key: string]: any }>;

    const { token, ...rest } = data;
    const tokenPreview = `${token.slice(0, 4)}…${token.slice(-4)}`;

    logger.info('getToken', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.TOKEN,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      logTraceId,
      correlationId,
      requestPayload,
      dpaaSResponse: rest,
      tokenPreview,
    });

    return token;
  } catch (error: unknown) {
    const err = error instanceof Error ? error : new Error(String(error));
    logDPaaSError(logTraceId, 'getToken', err.message);
    throw err;
  }
};

export const authorisationLookup = async (
  args: AuthLookupParams,
  logTraceId: string,
  correlationId: string,
): Promise<any> => {
  const paymentMethod = getPaymentMethod(args);
  if (
    args.authenticationIndicator &&
    args.authenticationIndicator !== AuthenticationIndicatorType.PREAUTH
  ) {
    const { preAuthAmount } = AppTenantConfig({
      paymentType: PaymentType.WALLET,
      appCountry: args.country,
    });
    if (preAuthAmount !== args.amount) {
      logger.info(
        `authorisationLookup: amount mismatch - expected ${preAuthAmount}, got ${args.amount}`,
        {
          serviceName: PaymentService.DPaaS,
          serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
          operationType: OperationType.Request,
          country: args.country,
          logTraceId,
          correlationId,
          userId: args.userId,
          requestPayload: args,
        },
      );
      throw new Error(
        `Amount mismatch: expected ${preAuthAmount}, got ${args.amount}`,
      );
    }
  }

  const provisioningParameters: ChallengeParameters = {
    amount:
      args.authenticationIndicator === AuthenticationIndicatorType.PREAUTH
        ? args.amount
        : 0,
    challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
    messageCategory: MessageCategory.PA,
  };

  const requestPayload: AuthorisationLookupData = {
    ...provisioningParameters,
    channel: 'sdk',
    currencyCode: CurrencyCode[args.country],
    dFReferenceId: args.referenceId,
    orderIdentifier: args.orderId,
    paymentMethod,
    authenticationIndicator:
      args.authenticationIndicator === AuthenticationIndicatorType.PREAUTH
        ? AuthenticationIndicator.PAYMENT_TRANSACTION
        : AuthenticationIndicator.ADD_CARD,
  };

  try {
    const { data: raw, status: serviceApiResponseCode } = (await callToDPaaS(
      args,
      requestPayload,
      ThreeDSUrl.AUTH_LOOKUP,
      correlationId,
    )) as AxiosResponse<any>;

    logger.info('authorisationLookup (full)', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      logTraceId,
      correlationId,
      userId: args.userId,
      requestPayload,
      response: raw,
    });

    const core3DS = {
      eciFlag: raw.eciFlag,
      enrolled: raw.enrolled,
      cavv: raw.cavv,
      threeDSServerTransactionId: raw.threeDSServerTransactionId,
      paresStatus: raw.paresStatus,
      threeDSVersion: raw.threeDSVersion,
      statusReason: raw.statusReason ?? '',
      dsTransactionId: raw.dsTransactionId,
      acsTransactionId: raw.acsTransactionId,
      errorNumber: raw.errorNumber,
    };

    logger.info('authorisationLookup core fields', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      logTraceId,
      correlationId,
      userId: args.userId,
      ...core3DS,
    });

    return raw;
  } catch (error: unknown) {
    const err = error instanceof Error ? error : new Error(String(error));
    logDPaaSError(logTraceId, 'authorisationLookup', err.message, args.userId);
    throw err;
  }
};

export const authenticate = async (
  args: AuthParams,
  logTraceId: string,
  correlationId: string,
): Promise<any> => {
  const requestPayload = {
    transactionId: args.transactionId,
    paymentMethod: getPaymentMethod(args),
  };

  try {
    const { data, status: serviceApiResponseCode } = (await callToDPaaS(
      args,
      requestPayload,
      ThreeDSUrl.AUTHENTICATE,
      correlationId,
    )) as AxiosResponse<any>;

    logger.info('authenticate', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTHENTICATE,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      logTraceId,
      correlationId,
      userId: args.userId,
      requestPayload,
      response: data,
    });

    return data;
  } catch (error: unknown) {
    const err = error instanceof Error ? error : new Error(String(error));
    logDPaaSError(logTraceId, 'authenticate', err.message, args.userId);
    throw err;
  }
};
