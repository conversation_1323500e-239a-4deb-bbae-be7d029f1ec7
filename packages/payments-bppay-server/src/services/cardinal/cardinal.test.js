import axios from 'axios';

import {
  AuthenticationIndicator,
  AuthenticationIndicatorType,
  ChallengeIndicator,
  MessageCategory,
} from '../../enums';
import logger from '../../utils/logger';
import { authenticate, authorisationLookup, getToken } from './cardinal';

const MyTokenType = {
  THREEDS: '3DS',
  WALLET: 'WALLET',
  PAY: 'PAY',
};
jest.mock('../../utils/helper');
jest.mock('axios');
jest.mock('../../env', () => ({}));
jest.mock('../../services/bpPay/appTenantConfig', () => ({
  __esModule: true,
  default: jest.fn().mockReturnValue({
    tenantConfig: 'aralme',
    preAuthAmount: 100,
  }),
}));
// remove the following line to see logger console logs during testing
jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.mock('@bp/pulse-common', () => ({
  TokenType: MyTokenType,
  getDPaaSAuthToken: jest.fn().mockReturnValue(Promise.resolve('12345')),
}));

const token =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2Mzg5OTQ1NjgsImlhdCI6MTYzODk5NDQ0OCwiaXNzIj' +
  'oiNjBhZmI1YTBlMThiMWIyMDliMzFhYzI4IiwianRpIjoiMDk0MjZjMmItMjZmMi00ODZkLTk5M2YtMjM3NDY5N2ZlZTk' +
  '4IiwiT3JnVW5pdElkIjoiNjBhNmJmYzFjZDlmZjY2NDAwZDk5ZTM5IiwiUGF5bG9hZCI6eyJPcmRlckRldGFpbHMiOnsi' +
  'QW1vdW50IjoiMTUwMCIsIkN1cnJlbmN5Q29kZSI6IkdCUCIsIk9yZGVyTnVtYmVyIjoiMGU1YzViZjItZWE2NC00MmU4LT' +
  'llZTEtNzFmZmY2NTIyZTE1In19fQ.J16TeRJ2v_uuggS6KYiRbjDf7A85whCySV-f8qLpDg4';

const params = {
  // amount: 1500,
  amount: 0,
  country: 'UK',
  orderId: '0e5c5bf2-ea64-42e8-9ee1-71fff6522e15',
};

const idParams = {
  ...params,
  referenceId: '1234567',
  paymentMethod: '1234567',
  paymentMethodType: 'ID',
  userId: '12345',
};

const tokenParams = {
  ...params,
  referenceId: '1234567',
  paymentMethod: '1234567',
  paymentMethodType: 'TOKEN',
  userId: '12345',
};

const authParams = {
  country: 'UK',
  transactionId: '234567',
  paymentMethod: '1234567',
  paymentMethodType: 'ID',
  userId: '12345',
};

const mockErrorMessage = 'Network issues';

const mockFn = () =>
  Promise.resolve({
    data: {
      token,
      tokenType: 'jwt',
    },
  });

const mockedPostFn = () =>
  Promise.resolve({
    data: authLookupResponse,
  });
const authLookupResponse = {
  eciFlag: '07',
  enrolled: 'Y',
  errorDescription: '',
  errorNumber: '0',
  orderId: '8000407590928610',
  transactionId: 'VbNvAyU8T3q0J1u6Llj0',
  threeDSVersion: '2.1.0',
  signatureVerification: 'Y',
  cardBrand: 'VISA',
  dsTransactionId: 'e440982e-9fe2-49d7-aaab-1f70d0a1569d',
  acsUrl:
    'https://0merchantacsstag.cardinalcommerce.com/MerchantACSWeb/creq.jsp',
  cavv: '',
  paresStatus: 'C',
  payload: token,
  authenticationType: '01',
  challengeRequired: 'Y',
  challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
  messageCategory: MessageCategory.PA,
  authenticationIndicator: AuthenticationIndicator.ADD_CARD,
  acsTransactionId: '0675efe3-16ae-4a05-92c5-375c7b0264e8',
  threeDSServerTransactionId: 'df75a28f-6ecf-47c3-b627-39a50442cf3e',
  acsReferenceNumber: 'Cardinal ACS',
  acsOperatorId: 'MerchantACS',
  sdkFlowType: 'CARDINAL',
};

describe('Calling cardinal api to get a jwt token', () => {
  it('returns success', async () => {
    axios.post.mockImplementationOnce(mockFn);
    const result = await getToken(params, 'logtraceid');
    expect(result).toBe(token);
  });
});

describe('Calling cardinal api to lookup auth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    axios.post.mockImplementation(mockFn);
  });
  it('returns success for a paymentMethodId', async () => {
    axios.post.mockImplementationOnce(mockedPostFn);
    const result = await authorisationLookup(idParams, 'logtraceid');
    expect(result).toMatchObject(authLookupResponse);
  });
  it('returns success for a paymentMethodToken', async () => {
    const mockedPost = axios.post.mockImplementationOnce(mockedPostFn);
    await authorisationLookup(tokenParams, 'logtraceid', '1234');
    expect(mockedPost).toHaveBeenCalledWith(
      'https://int-dpaas-3ds.digitalplatform.bp.com/b2c/3ds/dsp/payments/3ds/v1/us/cardinal/consumerauth/lookup',
      {
        // amount: 1500,
        amount: 0,
        channel: 'sdk',
        currencyCode: 'GBP',
        dFReferenceId: '1234567',
        orderIdentifier: '0e5c5bf2-ea64-42e8-9ee1-71fff6522e15',
        challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
        messageCategory: MessageCategory.PA,
        authenticationIndicator: AuthenticationIndicator.ADD_CARD,
        paymentMethod: {
          bpVault: {
            card: {
              token: '1234567',
            },
          },
        },
      },
      {
        headers: {
          Authorization: 'Bearer 12345',
          'x-tenant-country': 'GB',
          'x-tenant-id': 'aralme',
          'x-user-id': '12345',
          'x-correlation-id': '1234',
        },
      },
    );
  });
  it('overrides to mandatory challenge parameters for UK users', async () => {
    const ukParams = {
      ...idParams,
      amount: 1500,
      country: 'UK',
    };

    axios.post.mockImplementationOnce(mockedPostFn);
    await authorisationLookup(ukParams, 'logtraceid', 'corr-id');

    expect(axios.post).toHaveBeenCalledWith(
      'https://int-dpaas-3ds.digitalplatform.bp.com/b2c/3ds/dsp/payments/3ds/v1/us/cardinal/consumerauth/lookup',
      {
        amount: 0,
        channel: 'sdk',
        currencyCode: 'GBP',
        dFReferenceId: '1234567',
        orderIdentifier: '0e5c5bf2-ea64-42e8-9ee1-71fff6522e15',
        challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
        messageCategory: MessageCategory.PA,
        authenticationIndicator: AuthenticationIndicator.ADD_CARD,
        paymentMethod: {
          bpWallet: {
            paymentMethodId: '1234567',
            userId: '12345',
          },
        },
      },
      {
        headers: expect.objectContaining({
          Authorization: 'Bearer 12345',
          'x-tenant-country': 'GB',
          'x-tenant-id': 'aralme',
          'x-user-id': '12345',
          'x-correlation-id': 'corr-id',
        }),
      },
    );
  });

  it('overrides to mandatory challenge parameters for GB users', async () => {
    const gbParams = { ...idParams, amount: 1500, country: 'GB' };
    axios.post.mockImplementationOnce(mockedPostFn);
    await authorisationLookup(gbParams, 'logtraceid', 'corr-id');

    expect(axios.post).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        amount: 0,
        currencyCode: 'GBP',
        challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
        messageCategory: MessageCategory.PA,
      }),
      expect.any(Object),
    );
  });

  it('overrides to mandatory challenge parameters for DE users', async () => {
    const deParams = { ...idParams, amount: 1500, country: 'DE' };
    axios.post.mockImplementationOnce(mockedPostFn);
    await authorisationLookup(deParams, 'logtraceid', 'corr-id');

    expect(axios.post).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        amount: 0,
        currencyCode: 'EUR',
        challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
        messageCategory: MessageCategory.PA,
      }),
      expect.any(Object),
    );
  });

  it('overrides to mandatory challenge parameters for ES users', async () => {
    const esParams = { ...idParams, amount: 1500, country: 'ES' };
    axios.post.mockImplementationOnce(mockedPostFn);
    await authorisationLookup(esParams, 'logtraceid', 'corr-id');

    expect(axios.post).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        amount: 0,
        currencyCode: 'EUR',
        challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
        messageCategory: MessageCategory.PA,
      }),
      expect.any(Object),
    );
  });
});

describe('Calling cardinal api to authorise the 3ds check', () => {
  it('returns success', async () => {
    axios.post.mockImplementationOnce(() =>
      Promise.resolve({
        data: {
          acsTransactionId: 'cceb65a7-48d3-4998-b069-d165aea05a6c',
          authenticationType: '03',
          cardBin: '400000',
          cardBrand: 'VISA',
          cavv: 'MTIzNDU2Nzg5MDEyMzQ1Njc4OTA=',
          dsTransactionId: '23b21ec2-d465-4772-b111-b5c1a691ada6',
          eciFlag: '05',
          errorDescription: '',
          errorNumber: '0',
          interactionCounter: '01',
          paresStatus: 'Y',
          signatureVerification: 'Y',
          threeDSServerTransactionId: 'f29aeb3e-1e8a-4810-8984-a090c0ee7518',
          threeDSVersion: '2.1.0',
        },
      }),
    );
    const result = await authenticate(authParams, 'logtraceid');
    expect(result).toMatchObject({
      acsTransactionId: 'cceb65a7-48d3-4998-b069-d165aea05a6c',
      authenticationType: '03',
      cardBin: '400000',
      cardBrand: 'VISA',
      cavv: 'MTIzNDU2Nzg5MDEyMzQ1Njc4OTA=',
      dsTransactionId: '23b21ec2-d465-4772-b111-b5c1a691ada6',
      eciFlag: '05',
      errorDescription: '',
      errorNumber: '0',
      interactionCounter: '01',
      paresStatus: 'Y',
      signatureVerification: 'Y',
      threeDSServerTransactionId: 'f29aeb3e-1e8a-4810-8984-a090c0ee7518',
      threeDSVersion: '2.1.0',
    });
  });
});

describe('Error cases', () => {
  const mockFn = () => Promise.reject(new Error(mockErrorMessage));
  it('Failing to call cardinal api to authorise the 3ds check throws an error', async () => {
    axios.post.mockImplementationOnce(mockFn);
    await expect(authenticate(authParams, 'logtraceid')).rejects.toThrow(
      new Error(mockErrorMessage),
    );
  });
  it('Failing to call cardinal api to lookup auth throws an error', async () => {
    axios.post.mockImplementationOnce(mockFn);
    await expect(authorisationLookup(idParams, 'logtraceid')).rejects.toThrow(
      new Error(mockErrorMessage),
    );
  });
  it('Failing to call cardinal api to get a jwt token throws an error', async () => {
    axios.post.mockImplementationOnce(mockFn);
    await expect(getToken(params, 'logtraceid')).rejects.toThrow(
      new Error(mockErrorMessage),
    );
  });
});
describe('authorisationLookup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('should throw error on amount mismatch', async () => {
    await expect(
      authorisationLookup(
        {
          amount: 50,
          country: 'GB',
          orderId: 'order-2',
          userId: 'user-1',
          authenticationIndicator: 'SOMETHING',
          referenceId: 'ref-1',
        },
        'log-trace-id',
        'corr-id',
      ),
    ).rejects.toThrow('Amount mismatch: expected 100, got 50');
  });
  it('should call callToDPaaS with correct params', async () => {
    axios.post.mockImplementationOnce(() =>
      Promise.resolve({
        data: {},
        status: 200,
      }),
    );
    const args = {
      amount: 100,
      country: 'GB',
      orderId: 'order-2',
      userId: 'user-1',
      authenticationIndicator: AuthenticationIndicatorType.PREAUTH,
      referenceId: 'ref-1',
    };

    await authorisationLookup(args, 'trace-id', 'corr-id');

    expect(axios.post).toHaveBeenCalledWith(
      expect.any(String), // The URL
      expect.objectContaining({
        amount: 100,
        challengeIndicator: expect.any(String),
        messageCategory: expect.any(String),
        channel: 'sdk',
        currencyCode: expect.any(String),
        dFReferenceId: 'ref-1',
        orderIdentifier: 'order-2',
        paymentMethod: expect.any(Object),
        authenticationIndicator: AuthenticationIndicator.PAYMENT_TRANSACTION,
      }),
      expect.any(Object), // The headers
    );
  });
});
