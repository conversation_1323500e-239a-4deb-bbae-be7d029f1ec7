import {
  AuthenticationIndicator,
  AuthenticationIndicatorType,
  ChallengeIndicator,
  Countries,
  CurrencyCode,
  MessageCategory,
} from '../../enums';

export interface GetTokenParams {
  amount: number;
  country: Countries;
  orderId: string;
  userId?: string;
}

export enum ThreeDSPaymentMethodType {
  TOKEN = 'TOKEN',
  ID = 'ID',
}

export interface AuthLookupParams {
  amount: number;
  country: Countries;
  orderId: string;
  referenceId: string;
  paymentMethod: string;
  paymentMethodType: ThreeDSPaymentMethodType;
  userId: string;
  authenticationIndicator?: AuthenticationIndicatorType;
}

export interface AuthParams {
  country: Countries;
  transactionId: string;
  paymentMethod: string;
  paymentMethodType: ThreeDSPaymentMethodType;
  userId: string;
}

export interface IDpaasHeaders {
  headers: Record<string, unknown>;
}

export interface ChallengeParameters {
  amount: number;
  challengeIndicator: ChallengeIndicator;
  messageCategory: MessageCategory;
}

export interface AuthorisationLookupData extends ChallengeParameters {
  channel: 'sdk';
  currencyCode: CurrencyCode;
  dFReferenceId: string;
  orderIdentifier: string;
  paymentMethod: any;
  authenticationIndicator: AuthenticationIndicator;
  [key: string]: unknown;
}
export interface IThreeDS {
  eciFlag: string;
  enrolled: string;
  cavv: string;
  threeDSServerTransactionId: string;
  paresStatus: string;
  threeDSVersion: string;
  statusReason: string;
  dsTransactionId?: string;
  acsTransactionId?: string;
  errorNumber?: string;
}

export interface RawCardinal3DS {
  authenticate3DS(): IThreeDS;
}
