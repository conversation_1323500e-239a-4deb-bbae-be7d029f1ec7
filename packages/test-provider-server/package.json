{"name": "@bp/feature-test-provider-server", "version": "1.0.0", "private": true, "main": "./dist/index.js", "scripts": {"prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf dist/", "dev": "nodemon --config nodemon.json src/index.ts", "lint": "eslint . --ext .ts", "lint:fix": "npm run lint -- --fix", "start": "node ./dist/index.js", "test": "jest --passWithNoTests", "test:ci": "jest --ci --passWithNoTests", "types:check": "tsc --noEmit"}, "dependencies": {"apollo-server-koa": "^2.26.1", "axios": "^1.10.0", "cookies": "^0.7.3", "date-fns": "^2.16.1", "dotenv": "^8.2.0", "find-up": "^5.0.0", "graphql": "^14.4.2", "graphql-request": "^4.0.0", "graphql-tools": "^4.0.5", "ioredis": "^4.28.0", "koa": "2.15.4", "koa-bodyparser": "^4.3.0", "koa-helmet": "^5.0.0", "koa-router": "^7.4.0", "module-alias": "^2.2.1", "ramda": "^0.26.1", "schemaglue": "^4.0.4", "uuid": "^8.3.0", "winston": "^3.8.2"}, "devDependencies": {"@bp/eslint-plugin": "^0.0.3", "@types/ioredis": "^4.28.10", "@types/koa-helmet": "^6.0.4", "@types/koa-router": "^7.4.4", "@types/uuid": "^8.3.4", "eslint": "^8.20.0", "nodemon": "^2.0.20", "prettier": "2.7.1", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "overrides": {"koa": "2.15.4"}}