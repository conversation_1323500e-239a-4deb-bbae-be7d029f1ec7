import axios from 'axios';

import availableResponse from '../../../networking/htb/__mock-responses__/available.testData.json';
import tokenResponse from '../../../networking/htb/__mock-responses__/token.testData.json';
import unavailableResponse from '../../../networking/htb/__mock-responses__/unavailable.testData.json';
import { Provider } from '../../../types/graphql';
import { contextMock } from '../../../utils/mock';
import { hasToBeAvailability } from './availability';

jest.mock('@elastic/elasticsearch');
jest.mock('axios');
jest.mock('../../../env');

const mockedAxios = jest.mocked(axios);

describe('hasToBeAvailability()', () => {
  const id = '74516cfe-9d32-11e5-8xdf7005056b332c9';

  const chargepoint: any = {
    apolloInternalId: `${Provider.HTB}-74516cfe-9d32-11e5-8xdf7005056b332c9`,
    apolloExternalId: `${Provider.HTB}-74516cfe-xxx-11e5-8xdf7005056b332c9`,
    provider: Provider.HTB,
    providerInternalId: id,
    connectors: [{ connectorInternalId: id, connectorExternalId: id }],
  };

  it('should make a request to the hastobe service for chargepoint details', async () => {
    mockedAxios.post
      .mockResolvedValueOnce(tokenResponse)
      .mockResolvedValueOnce(availableResponse);

    const result = await hasToBeAvailability(contextMock(), chargepoint);

    expect(result).toEqual({
      apolloInternalId: `${Provider.HTB}-74516cfe-9d32-11e5-8xdf7005056b332c9`,
      provider: Provider.HTB,
      available: 'Available',
      connectors: [
        {
          connectorInternalId: id,
          connectorExternalId: id,
          state: 'Available',
        },
      ],
    });
  });

  it('should set connectors as unavailable when chargepoint is unavailable', async () => {
    mockedAxios.post
      .mockResolvedValueOnce(tokenResponse)
      .mockResolvedValueOnce(unavailableResponse);

    const result = await hasToBeAvailability(contextMock(), chargepoint);

    expect(result).toEqual({
      apolloInternalId: `${Provider.HTB}-74516cfe-9d32-11e5-8xdf7005056b332c9`,
      provider: Provider.HTB,
      available: 'Unavailable',
      connectors: [
        {
          connectorInternalId: id,
          connectorExternalId: id,
          state: 'Unavailable',
        },
      ],
    });
  });
});
