import type {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

import { logger } from './logger';

interface TimedAxiosRequestConfig extends InternalAxiosRequestConfig {
  startAt?: number;
}

export function onFulfilledRequest(cfg: TimedAxiosRequestConfig) {
  cfg.startAt = Date.now();
  return cfg;
}

export function onFulfilledResponse(res: AxiosResponse) {
  const cfg = res.config as TimedAxiosRequestConfig;
  const method = cfg.method?.toUpperCase();
  const size = res.headers['content-length'];
  const duration =
    cfg.startAt !== undefined ? `${Date.now() - cfg.startAt} ms` : '';

  logger.info(
    `${method} ${cfg.url} ${res.status} ${duration} - ${size} bytes`,
    { headers: cfg.headers },
  );

  return res;
}

export function onRejectedResponse(error: AxiosError) {
  const cfg = error.config;
  const res = error.response;
  const method = cfg?.method?.toUpperCase();
  const status = res?.status ? `${res?.status} ` : '';

  logger.error(`${method} ${cfg?.url} ${status}- ${error.message}`);

  return Promise.reject(error);
}
