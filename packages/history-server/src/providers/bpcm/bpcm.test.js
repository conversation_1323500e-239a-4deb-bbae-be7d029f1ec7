/* eslint-disable sonarjs/no-duplicate-string */
import axios from 'axios';
import { format } from 'date-fns';

import { logger } from '../../utils/logger.ts';
import { getHistoryByStartDateRange } from './bpcm.ts';
import {
  data,
  emptyData,
  mockBPCMHistoryItemPostMapResult,
  mockBPCMHistoryRecord,
  mockGetReceiptDetails,
} from './mocks.ts';

const mockFormat = (date, format) => format(new Date(date), format);

jest.mock('axios');

jest.mock('../../env', () => ({}));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

jest.mock('../../utils/utils', () => ({
  ...jest.requireActual('../../utils/utils'),
  addReceiptId: (res) => res,
}));
jest.mock('../../services/receipt/getReceiptDetail', () => ({
  getReceiptDetails: () => mockGetReceiptDetails,
}));

describe('getHistoryByStartDateRange', () => {
  it('should sanitise charge history data from bpcm', async () => {
    axios.post.mockImplementationOnce(() => Promise.resolve(data));

    const history = await getHistoryByStartDateRange({
      userId: 'dummy',
      startDate: '2020-07-17T13:45:00.000Z',
      endDate: '2020-07-17T13:55:00.000Z',
      logId: 'dummy',
    });

    // need to stringify because fails to see equivalence between two arrays
    expect(history.results[0]).toEqual(mockBPCMHistoryItemPostMapResult);
  });

  it('should throw an error if the call to bpcm fails', async () => {
    const errorMessage = 'Network Error';
    axios.post.mockImplementationOnce(() =>
      Promise.reject(new Error(errorMessage)),
    );

    await expect(
      getHistoryByStartDateRange({
        userId: 'dummy',
        startDate: '2020-07-17T13:45:00.000Z',
        endDate: '2020-07-17T13:15:00.000Z',
        logTraceId: 'dummy',
      }),
    ).rejects.toThrow(errorMessage);
  });

  it('should return an empty array if bpcm has no data', async () => {
    axios.post.mockImplementationOnce(() => Promise.resolve(emptyData));

    const history = await getHistoryByStartDateRange({
      userId: 'dummy',
      startDate: '2020-07-17T13:45:00.000Z',
      endDate: '2020-07-17T13:15:00.000Z',
      logTraceId: 'dummy',
    });
    expect(history.results).toEqual([]);
    expect(history.count).toEqual(0);
  });

  it('should sort the data in time order', async () => {
    const newData = data;
    const newChargeActivity = {
      charge_history_item: {
        charge_id: 94774,
        cdr_filename: 'cdrFilename',
        tag_serial: 'PAYG_123',
        timestamp: {
          start: '2023-01-13T14:57:15.050Z',
          stop: '2023-01-13T15:27:15.050Z',
        },
        chargepoint: {
          serial: 'BPU_2002',
          country: 'UK',
          displayAddress: 'Breckland, Linford Wood',
          grossUnitCost: '0.18',
          publicName: 'bp pulse',
          address: 'Haywards Heath Golf Course',
          socket: {
            type: '13A',
            number: 1,
            rating: 25,
            phase: 'AC',
          },
          blockingFee: {
            duration: 90,
            price: 0,
          },
        },
        charge: {
          currency: 'GBP',
          charge_gross: 1.2,
          charge_net: 1,
          charge_vat: 0.2,
          charge_vat_percentage: 0.2,
          gross_amount_local_currency: 1.2,
          chargeAdditionalFees: 0,
          chargeBaseFee: 1,
          energy_used: 3.8882,
          unit_cost: '0.18',
          unit_min_cost: '1.20',
          overstay: {
            fine_unit_cost: 0,
            fine_gross: 0,
            fine_net: 0,
            fine_vat: 0,
            fine_vat_percentage: 0,
            duration: 0,
          },
          isStandardInvoice: false,
          total_gross: 1.2,
        },
      },
    };

    newData.data.payload.charge_history = [
      ...newData.data.payload.charge_history,
      newChargeActivity,
    ];
    axios.post.mockImplementationOnce(() => Promise.resolve(newData));

    const history = await getHistoryByStartDateRange({
      userId: 'dummy',
      startDate: '2020-07-17T13:45:00.000Z',
      endDate: '2020-07-21T13:15:00.000Z',
      logTraceId: 'dummy',
    });

    expect(history.results[1]).toEqual(mockBPCMHistoryRecord);
  });

  it('should return the correct 24[0-23]hour date-time format', async () => {
    jest.mock('date-fns', () => ({
      format: (date) => mockFormat(new Date(date), "yyyy-LL-dd'T'HH:mm:ssxxx"),
    }));

    const computedDate = format(
      new Date('2023-03-22T00:22:00.000Z'),
      "yyyy-LL-dd'T'HH:mm:ssxxx",
    );

    expect(computedDate).toBe('2023-03-22T00:22:00+00:00');
  });
});
